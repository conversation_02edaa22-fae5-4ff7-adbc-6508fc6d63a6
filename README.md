# Veritas Agent - Business Automation Platform

A modern web application for business automation agents with a clean, modular JavaScript architecture.

## 🏗️ Project Structure

```
veritas-agent/
├── public/
│   ├── index.html              # Main HTML file
│   ├── css/
│   │   └── styles.css          # Main stylesheet
│   ├── js/                     # Modular JavaScript files
│   │   ├── main.js             # Application initialization & routing
│   │   ├── api.js              # Supabase client & data fetching
│   │   ├── auth.js             # Authentication logic
│   │   ├── dashboard.js        # Dashboard functionality
│   │   ├── modals.js           # Modal interactions
│   │   └── utils.js            # Utility functions
│   └── images/                 # Static assets
├── api/
│   └── contact.js              # Serverless contact form handler
├── package.json                # Project configuration
└── README.md                   # This file
```

## 🚀 Features

- **Modern UI/UX**: Clean, responsive design with Tailwind CSS
- **Authentication**: Secure user authentication with Supabase
- **Real-time Dashboard**: Live metrics and data visualization
- **Modular Architecture**: Well-organized JavaScript modules
- **Contact Forms**: Integrated contact and support forms
- **Modal System**: Interactive capability details and forms
- **Mobile Responsive**: Works seamlessly on all devices

## 📦 JavaScript Modules

### `main.js` - Application Core
- Page routing and navigation
- Section visibility management
- Initial application setup
- Event delegation for navigation

### `api.js` - Data Layer
- Supabase client initialization
- Real-time data updates
- Dashboard metrics management
- Error logging and handling

### `auth.js` - Authentication
- Login/logout functionality
- User registration
- Profile management
- Session handling

### `dashboard.js` - Dashboard Features
- Dashboard view switching
- Chart rendering with Chart.js
- Profile updates
- Settings management

### `modals.js` - Modal System
- Capability detail modals
- Profile menu interactions
- Loading overlays
- Generic modal utilities

### `utils.js` - Utilities
- Form validation
- Toast notifications
- Local storage management
- Date formatting
- Helper functions

## 🛠️ Setup & Development

### Prerequisites
- Node.js (v14 or higher)
- A Supabase account and project

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd veritas-agent
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure Supabase**
   - Update the Supabase URL and API key in `public/js/api.js`
   - Set up your database schema (see Database Setup below)

4. **Start development server**
   ```bash
   npm run dev
   ```
   The application will be available at `http://localhost:3000`

### Production Build

```bash
npm run build
npm start
```

## 🗄️ Database Setup

### Supabase Configuration

1. **Create a new Supabase project**

2. **Set up the user_profiles table**:
   ```sql
   CREATE TABLE user_profiles (
     user_id UUID REFERENCES auth.users ON DELETE CASCADE,
     full_name TEXT,
     company_name TEXT,
     role TEXT DEFAULT 'Client' CHECK (role IN ('Owner', 'Client')),
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     PRIMARY KEY (user_id)
   );
   ```

3. **Set up the companies table**:
   ```sql
   CREATE TABLE companies (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     name TEXT UNIQUE NOT NULL,
     user_count INTEGER DEFAULT 0,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );
   ```

4. **Set up the ai_agents table**:
   ```sql
   CREATE TABLE ai_agents (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     name TEXT NOT NULL,
     company_name TEXT NOT NULL,
     created_by UUID REFERENCES auth.users ON DELETE CASCADE,
     status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending')),
     description TEXT,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );
   ```

5. **Set up the user_integrations table**:
   ```sql
   CREATE TABLE user_integrations (
     user_id UUID REFERENCES auth.users ON DELETE CASCADE,
     service_name TEXT NOT NULL,
     status TEXT DEFAULT 'disconnected' CHECK (status IN ('connected', 'disconnected', 'pending')),
     connected_at TIMESTAMP WITH TIME ZONE,
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     PRIMARY KEY (user_id, service_name)
   );
   ```

6. **Set up the activity_logs table** (for notifications and activity tracking):
   ```sql
   CREATE TABLE activity_logs (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     user_id UUID REFERENCES auth.users ON DELETE CASCADE,
     category TEXT NOT NULL CHECK (category IN ('system', 'integration', 'agents', 'user')),
     type TEXT NOT NULL,
     title TEXT NOT NULL,
     message TEXT NOT NULL,
     metadata JSONB DEFAULT '{}',
     is_read BOOLEAN DEFAULT FALSE,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );

   -- Create indexes for better performance
   CREATE INDEX idx_activity_logs_user_id ON activity_logs(user_id);
   CREATE INDEX idx_activity_logs_category ON activity_logs(category);
   CREATE INDEX idx_activity_logs_is_read ON activity_logs(is_read);
   CREATE INDEX idx_activity_logs_created_at ON activity_logs(created_at DESC);
   CREATE INDEX idx_activity_logs_user_unread ON activity_logs(user_id, is_read) WHERE is_read = FALSE;
   ```

7. **Create triggers for automatic profile and company creation**:
   ```sql
   -- Function to handle new user registration
   CREATE OR REPLACE FUNCTION public.handle_new_user()
   RETURNS TRIGGER AS $$
   BEGIN
     -- Wait a moment to ensure user metadata is available
     PERFORM pg_sleep(1);

     -- Insert user profile
     INSERT INTO public.user_profiles (user_id, full_name, company_name)
     VALUES (
       NEW.id,
       NEW.raw_user_meta_data->>'full_name',
       NEW.raw_user_meta_data->>'company'
     );

     -- Insert or update company
     INSERT INTO public.companies (name, user_count)
     VALUES (NEW.raw_user_meta_data->>'company', 1)
     ON CONFLICT (name)
     DO UPDATE SET
       user_count = companies.user_count + 1,
       updated_at = NOW();

     RETURN NEW;
   END;
   $$ LANGUAGE plpgsql SECURITY DEFINER;

   -- Trigger for new user creation
   CREATE TRIGGER on_auth_user_created
     AFTER INSERT ON auth.users
     FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
   ```

8. **Set up Row Level Security (RLS) - DISABLED FOR DEMO**:
   ```sql
   -- Disable RLS for demo purposes (enable in production)
   ALTER TABLE user_profiles DISABLE ROW LEVEL SECURITY;
   ALTER TABLE companies DISABLE ROW LEVEL SECURITY;
   ALTER TABLE ai_agents DISABLE ROW LEVEL SECURITY;
   ALTER TABLE user_integrations DISABLE ROW LEVEL SECURITY;
   ALTER TABLE activity_logs DISABLE ROW LEVEL SECURITY;

   -- For production, enable RLS and create policies:
   -- ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
   -- CREATE POLICY "Users can view own profile" ON user_profiles
   --   FOR SELECT USING (auth.uid() = user_id);
   -- CREATE POLICY "Users can update own profile" ON user_profiles
   --   FOR UPDATE USING (auth.uid() = user_id);
   --
   -- ALTER TABLE activity_logs ENABLE ROW LEVEL SECURITY;
   -- CREATE POLICY "Users can view own activity logs" ON activity_logs
   --   FOR SELECT USING (auth.uid() = user_id);
   -- CREATE POLICY "Users can update own activity logs" ON activity_logs
   --   FOR UPDATE USING (auth.uid() = user_id);
   ```

9. **Optional: Set up automatic activity logging triggers**:
   For complete activity tracking, run the SQL commands in `database_setup_activity_logs.sql` which includes:
   - Automatic login activity logging
   - Integration activity tracking
   - AI agent activity logging
   - Profile update tracking

## 🔧 Configuration

### Environment Variables

For serverless deployment, set these environment variables:

- `N8N_WEBHOOK_URL`: Your N8N webhook URL for contact form submissions

### Supabase Settings

Update the following in `public/js/api.js`:
```javascript
const SUPABASE_URL = 'your-supabase-url';
const SUPABASE_ANON_KEY = 'your-supabase-anon-key';

## 📱 Deployment

### Static Hosting (Netlify, Vercel, etc.)

1. Build the project: `npm run build`
2. Deploy the `public/` directory
3. Set up environment variables for the contact form API

### Serverless Functions

The `api/contact.js` file provides serverless function implementations for:
- Vercel Edge Functions
- Netlify Functions
- Standard Node.js serverless platforms

## 🎨 Customization

### Styling
- Main styles are in `public/css/styles.css`
- Uses Tailwind CSS for utility classes
- Custom CSS for animations and components

### Branding
- Update colors in the CSS file
- Replace logo and images in `public/images/`
- Modify company information in the HTML

### Features
- Add new dashboard views in `dashboard.js`
- Create new modals in `modals.js`
- Extend API functionality in `api.js`

## 🧪 Testing

Run the development server and test:
- User registration and login
- Dashboard functionality
- Contact form submission
- Modal interactions
- Responsive design

## 📄 License

MIT License - see LICENSE file for details

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For support and questions:

- Documentation: [Link to docs]
- Issues: [GitHub Issues]

---

Built with ❤️ for modern business automation
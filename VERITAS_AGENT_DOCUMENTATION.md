# Veritas Agent - Complete Application Documentation

## 📋 Overview

**Veritas Agent** is a comprehensive Business Automation Platform designed to help companies deploy and manage AI-powered automation agents. The application provides a modern web interface for businesses to create, monitor, and integrate AI agents with their existing software ecosystem.

## 🏗️ Architecture & Technology Stack

### Frontend Architecture
- **Framework**: Vanilla JavaScript with modular architecture
- **Styling**: Tailwind CSS with custom CSS animations
- **UI Components**: Custom modal system, responsive design
- **Charts**: Chart.js for data visualization
- **Icons**: Font Awesome 6.4.0
- **Fonts**: Inter font family from Google Fonts

### Backend & Infrastructure
- **Database**: Supabase (PostgreSQL with real-time capabilities)
- **Authentication**: <PERSON>pabase Auth with JWT tokens
- **Automation**: N8N for workflow automation and contact form processing
- **Hosting**: Static site hosting (Netlify/Vercel compatible)
- **API**: Serverless functions for contact form handling

### Development Environment
- **Package Manager**: npm
- **Development Server**: http-server
- **Build Process**: Static site (no build step required)
- **Version Control**: Git

## 🗄️ Database Schema

### Core Tables

#### 1. `user_profiles`
```sql
- user_id (UUID, FK to auth.users)
- full_name (TEXT)
- company_name (TEXT)
- role (TEXT: 'Owner' | 'Client')
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

#### 2. `companies`
```sql
- id (UUID, Primary Key)
- name (TEXT, UNIQUE)
- user_count (INTEGER)
- description (TEXT, optional)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

#### 3. `ai_agents`
```sql
- id (UUID, Primary Key)
- name (TEXT)
- company_name (TEXT)
- created_by (UUID, FK to auth.users)
- status (TEXT: 'active' | 'inactive' | 'pending')
- description (TEXT)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

#### 4. `user_integrations`
```sql
- user_id (UUID, FK to auth.users)
- service_name (TEXT)
- status (TEXT: 'connected' | 'disconnected' | 'pending')
- connected_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

#### 5. `activity_logs`
```sql
- id (UUID, Primary Key)
- user_id (UUID, FK to auth.users)
- category (TEXT: 'system' | 'integration' | 'agents' | 'user')
- type (TEXT)
- title (TEXT)
- message (TEXT)
- metadata (JSONB)
- is_read (BOOLEAN)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

## 🎯 Core Features

### 1. Authentication System
- **User Registration**: Email/password with profile creation
- **Login/Logout**: Secure session management
- **Role-based Access**: Owner vs Client permissions
- **Profile Management**: User details and company association

### 2. Dashboard System
- **Overview Dashboard**: Real-time statistics and metrics
- **Company Management**: (Owner role) Manage multiple companies
- **AI Agent Management**: (Owner role) Create and monitor AI agents
- **Client Management**: (Owner role) View and manage client users
- **Notifications**: (Client role) Activity logs and system notifications
- **Integrations**: (Client role) Connect external services
- **Help & Support**: (Client role) Support resources and FAQ

### 3. Real-time Data
- **Live Statistics**: Companies, active agents, total users
- **Real-time Updates**: WebSocket connections for live data
- **Dashboard Metrics**: Chart.js visualizations
- **Status Monitoring**: Agent and integration status tracking

### 4. Integration System
Currently supports connections to:
- **Gmail**: Email integration via OAuth2
- **Google Sheets**: Spreadsheet data access
- **Slack**: Team communication
- **Telegram**: Messaging platform
- **Notion**: Knowledge management
- **Airtable**: Database platform
- **MySQL/PostgreSQL**: Database connections
- **HTTP APIs**: Custom API integrations

### 5. Contact & Support System
- **Contact Forms**: N8N webhook integration
- **Support Tickets**: Email-based support system
- **FAQ System**: Interactive help documentation
- **Status Information**: Real-time agent and integration status

## 📁 File Structure

```
veritas-agent/
├── public/
│   ├── index.html              # Main application file
│   ├── css/
│   │   └── style.css          # Custom styles and animations
│   ├── js/                    # Modular JavaScript architecture
│   │   ├── main.js            # App initialization & routing
│   │   ├── api.js             # Supabase client & data layer
│   │   ├── auth.js            # Authentication logic
│   │   ├── dashboard.js       # Dashboard functionality
│   │   ├── modals.js          # Modal system
│   │   ├── utils.js           # Utility functions
│   │   ├── constellation.js   # Background animations
│   │   └── profile-debug.js   # Debug utilities
│   └── images/                # Static assets
├── api/
│   └── contact.js             # Serverless contact handler
├── package.json               # Project configuration
└── README.md                  # Basic setup instructions
```

## 🔐 Security & Permissions

### Row Level Security (RLS)
- **Current State**: Disabled for demo purposes
- **Production Ready**: RLS policies defined but not enabled
- **User Isolation**: Policies ensure users only access their data

### Role-based Access Control
- **Owner Role**: Full access to company management, AI agents, clients
- **Client Role**: Access to integrations, help & support, personal dashboard
- **Authentication**: JWT-based session management via Supabase

## 🚀 Key Workflows

### 1. User Onboarding
1. User registers with email/password and company info
2. Automatic profile and company creation via database triggers
3. Role assignment (Owner for new companies, Client for existing)
4. Dashboard access based on role permissions

### 2. AI Agent Creation (Owner)
1. Navigate to AI Agent Management
2. Select company and provide agent details
3. Agent created with 'active' status
4. Real-time dashboard updates reflect new agent

### 3. Integration Setup (Client)
1. Access Integrations page
2. Select service (Gmail, Google Sheets, etc.)
3. OAuth2 flow or credential input
4. Status tracking in user_integrations table

### 4. Contact Form Processing
1. User submits contact form
2. Serverless function validates data
3. N8N webhook receives form data
4. Automated workflow processes inquiry

### 5. Notifications & Activity Tracking (Client)
1. Real-time activity logging for all user actions
2. Categorized notifications (System, Integrations, AI Agents, User)
3. Read/unread status tracking with notification badges
4. Filterable activity feed with statistics
5. Automatic logging of login, integration changes, and profile updates

## 🔧 Configuration

### Environment Variables
- `N8N_WEBHOOK_URL`: Webhook endpoint for contact forms
- `SUPABASE_URL`: Supabase project URL
- `SUPABASE_ANON_KEY`: Supabase anonymous key

### Deployment Options
- **Static Hosting**: Netlify, Vercel, GitHub Pages
- **Serverless Functions**: Contact form API handlers
- **Database**: Supabase cloud or self-hosted

## 🎨 UI/UX Features

### Design System
- **Dark Theme**: Modern dark UI with blue accents
- **Responsive Design**: Mobile-first approach
- **Animations**: Smooth transitions and loading states
- **Typography**: Inter font for clean readability

### Interactive Elements
- **Modal System**: Detailed capability information
- **Loading States**: Progress indicators and overlays
- **Toast Notifications**: User feedback system
- **Real-time Updates**: Live data without page refresh

## 📊 Business Intelligence

### Dashboard Metrics
- **Company Statistics**: Total companies and user counts
- **Agent Performance**: Active vs inactive agents
- **User Engagement**: Registration and activity tracking
- **Integration Status**: Connected services overview

### Reporting Capabilities
- **Real-time Charts**: Visual data representation
- **Status Monitoring**: System health indicators
- **User Analytics**: Company and agent usage patterns

## 🔄 Integration with N8N

### Workflow Automation
- **Contact Processing**: Automated lead management
- **Email Notifications**: Support ticket routing
- **Data Synchronization**: Cross-platform data flow
- **Custom Workflows**: Business-specific automation

### Webhook Integration
- **Contact Forms**: Direct N8N webhook calls
- **Event Triggers**: User actions trigger workflows
- **Data Processing**: Automated data transformation

## 🛠️ Development & Maintenance

### Code Organization
- **Modular Architecture**: Separated concerns across files
- **Event-driven**: Clean event handling and delegation
- **Error Handling**: Comprehensive error logging
- **Debug Support**: Built-in debugging utilities

### Performance Optimization
- **Lazy Loading**: On-demand resource loading
- **Caching**: Local storage for user preferences
- **Real-time Efficiency**: Optimized WebSocket usage
- **Responsive Images**: Optimized asset delivery

## 📈 Scalability Considerations

### Database Scaling
- **Supabase**: Built-in scaling capabilities
- **Indexing**: Optimized queries for performance
- **Real-time**: Efficient subscription management

### Application Scaling
- **Static Assets**: CDN-friendly architecture
- **Serverless**: Auto-scaling contact form handlers
- **Modular Code**: Easy feature addition and maintenance

---

**Built by**: Bradley Bulman  
**License**: MIT  
**Version**: 1.0.0  
**Last Updated**: 2024

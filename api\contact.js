// N8N Webhook Proxy for Serverless Functions
// This file handles contact form submissions and forwards them to N8N webhook

export default async function handler(req, res) {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    // Only allow POST requests
    if (req.method !== 'POST') {
        return res.status(405).json({ 
            error: 'Method not allowed',
            message: 'Only POST requests are accepted'
        });
    }

    try {
        const { name, company, email, message } = req.body;

        // Validate required fields
        if (!name || !company || !email || !message) {
            return res.status(400).json({
                error: 'Missing required fields',
                message: 'Name, company, email, and message are required'
            });
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({
                error: 'Invalid email format',
                message: 'Please provide a valid email address'
            });
        }

        // N8N webhook URL (replace with your actual webhook URL)
        const N8N_WEBHOOK_URL = process.env.N8N_WEBHOOK_URL || 'https://your-n8n-instance.com/webhook/contact';

        // Prepare data for N8N
        const webhookData = {
            name: name.trim(),
            company: company.trim(),
            email: email.trim().toLowerCase(),
            message: message.trim(),
            timestamp: new Date().toISOString(),
            source: 'veritas-agent-website',
            ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
            userAgent: req.headers['user-agent']
        };

        // Send to N8N webhook
        const response = await fetch(N8N_WEBHOOK_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Veritas-Agent-Contact-Form/1.0'
            },
            body: JSON.stringify(webhookData)
        });

        if (!response.ok) {
            throw new Error(`N8N webhook responded with status: ${response.status}`);
        }

        // Log successful submission (optional)
        console.log(`Contact form submitted successfully: ${email} from ${company}`);

        // Return success response
        return res.status(200).json({
            success: true,
            message: 'Thank you for your message! We\'ll get back to you soon.',
            timestamp: webhookData.timestamp
        });

    } catch (error) {
        console.error('Contact form submission error:', error);

        // Return error response
        return res.status(500).json({
            error: 'Internal server error',
            message: 'Failed to process your request. Please try again later.',
            timestamp: new Date().toISOString()
        });
    }
}

// Alternative implementation for different serverless platforms
export const config = {
    api: {
        bodyParser: {
            sizeLimit: '1mb',
        },
    },
};

// For Netlify Functions
export const netlifyHandler = async (event, context) => {
    const { httpMethod, body, headers } = event;

    // Set CORS headers
    const corsHeaders = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    // Handle preflight requests
    if (httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers: corsHeaders,
            body: '',
        };
    }

    // Only allow POST requests
    if (httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers: corsHeaders,
            body: JSON.stringify({
                error: 'Method not allowed',
                message: 'Only POST requests are accepted'
            }),
        };
    }

    try {
        const data = JSON.parse(body);
        const { name, company, email, message } = data;

        // Validate required fields
        if (!name || !company || !email || !message) {
            return {
                statusCode: 400,
                headers: corsHeaders,
                body: JSON.stringify({
                    error: 'Missing required fields',
                    message: 'Name, company, email, and message are required'
                }),
            };
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return {
                statusCode: 400,
                headers: corsHeaders,
                body: JSON.stringify({
                    error: 'Invalid email format',
                    message: 'Please provide a valid email address'
                }),
            };
        }

        // N8N webhook URL
        const N8N_WEBHOOK_URL = process.env.N8N_WEBHOOK_URL || 'https://your-n8n-instance.com/webhook/contact';

        // Prepare data for N8N
        const webhookData = {
            name: name.trim(),
            company: company.trim(),
            email: email.trim().toLowerCase(),
            message: message.trim(),
            timestamp: new Date().toISOString(),
            source: 'veritas-agent-website',
            ip: headers['x-forwarded-for'] || headers['client-ip'],
            userAgent: headers['user-agent']
        };

        // Send to N8N webhook
        const response = await fetch(N8N_WEBHOOK_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Veritas-Agent-Contact-Form/1.0'
            },
            body: JSON.stringify(webhookData)
        });

        if (!response.ok) {
            throw new Error(`N8N webhook responded with status: ${response.status}`);
        }

        // Log successful submission
        console.log(`Contact form submitted successfully: ${email} from ${company}`);

        // Return success response
        return {
            statusCode: 200,
            headers: corsHeaders,
            body: JSON.stringify({
                success: true,
                message: 'Thank you for your message! We\'ll get back to you soon.',
                timestamp: webhookData.timestamp
            }),
        };

    } catch (error) {
        console.error('Contact form submission error:', error);

        // Return error response
        return {
            statusCode: 500,
            headers: corsHeaders,
            body: JSON.stringify({
                error: 'Internal server error',
                message: 'Failed to process your request. Please try again later.',
                timestamp: new Date().toISOString()
            }),
        };
    }
};

// For Vercel Edge Functions
export const vercelEdgeHandler = async (request) => {
    // Set CORS headers
    const corsHeaders = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    // Handle preflight requests
    if (request.method === 'OPTIONS') {
        return new Response(null, {
            status: 200,
            headers: corsHeaders,
        });
    }

    // Only allow POST requests
    if (request.method !== 'POST') {
        return new Response(JSON.stringify({
            error: 'Method not allowed',
            message: 'Only POST requests are accepted'
        }), {
            status: 405,
            headers: {
                ...corsHeaders,
                'Content-Type': 'application/json',
            },
        });
    }

    try {
        const data = await request.json();
        const { name, company, email, message } = data;

        // Validate required fields
        if (!name || !company || !email || !message) {
            return new Response(JSON.stringify({
                error: 'Missing required fields',
                message: 'Name, company, email, and message are required'
            }), {
                status: 400,
                headers: {
                    ...corsHeaders,
                    'Content-Type': 'application/json',
                },
            });
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return new Response(JSON.stringify({
                error: 'Invalid email format',
                message: 'Please provide a valid email address'
            }), {
                status: 400,
                headers: {
                    ...corsHeaders,
                    'Content-Type': 'application/json',
                },
            });
        }

        // N8N webhook URL
        const N8N_WEBHOOK_URL = process.env.N8N_WEBHOOK_URL || 'https://your-n8n-instance.com/webhook/contact';

        // Prepare data for N8N
        const webhookData = {
            name: name.trim(),
            company: company.trim(),
            email: email.trim().toLowerCase(),
            message: message.trim(),
            timestamp: new Date().toISOString(),
            source: 'veritas-agent-website',
            ip: request.headers.get('x-forwarded-for') || request.headers.get('cf-connecting-ip'),
            userAgent: request.headers.get('user-agent')
        };

        // Send to N8N webhook
        const response = await fetch(N8N_WEBHOOK_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Veritas-Agent-Contact-Form/1.0'
            },
            body: JSON.stringify(webhookData)
        });

        if (!response.ok) {
            throw new Error(`N8N webhook responded with status: ${response.status}`);
        }

        // Log successful submission
        console.log(`Contact form submitted successfully: ${email} from ${company}`);

        // Return success response
        return new Response(JSON.stringify({
            success: true,
            message: 'Thank you for your message! We\'ll get back to you soon.',
            timestamp: webhookData.timestamp
        }), {
            status: 200,
            headers: {
                ...corsHeaders,
                'Content-Type': 'application/json',
            },
        });

    } catch (error) {
        console.error('Contact form submission error:', error);

        // Return error response
        return new Response(JSON.stringify({
            error: 'Internal server error',
            message: 'Failed to process your request. Please try again later.',
            timestamp: new Date().toISOString()
        }), {
            status: 500,
            headers: {
                ...corsHeaders,
                'Content-Type': 'application/json',
            },
        });
    }
};

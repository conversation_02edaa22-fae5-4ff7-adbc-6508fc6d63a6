// Support Ticket API Endpoint
// Handles support requests from the dashboard Help & Support page

export default async function handler(req, res) {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        return res.status(200).end();
    }

    // Only allow POST requests
    if (req.method !== 'POST') {
        return res.status(405).json({
            error: 'Method not allowed',
            message: 'Only POST requests are allowed'
        });
    }

    try {
        const { 
            subject, 
            priority, 
            message, 
            user_email, 
            user_name, 
            user_company, 
            via_email_integration 
        } = req.body;

        // Validate required fields
        if (!subject || !message || !user_email || !user_name) {
            return res.status(400).json({
                error: 'Missing required fields',
                message: 'Subject, message, user email, and user name are required'
            });
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(user_email)) {
            return res.status(400).json({
                error: 'Invalid email format',
                message: 'Please provide a valid email address'
            });
        }

        // N8N webhook URL for support tickets
        const N8N_SUPPORT_WEBHOOK_URL = process.env.N8N_SUPPORT_WEBHOOK_URL || process.env.N8N_WEBHOOK_URL || 'https://your-n8n-instance.com/webhook/support';

        // Prepare data for N8N with support-specific formatting
        const webhookData = {
            type: 'support_ticket',
            subject: subject.trim(),
            priority: priority || 'medium',
            message: message.trim(),
            user_name: user_name.trim(),
            user_email: user_email.trim().toLowerCase(),
            user_company: user_company || 'Not specified',
            via_email_integration: via_email_integration || false,
            timestamp: new Date().toISOString(),
            source: 'veritas-agent-dashboard',
            ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
            userAgent: req.headers['user-agent']
        };

        // Send to N8N webhook
        const response = await fetch(N8N_SUPPORT_WEBHOOK_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Veritas-Agent-Support/1.0'
            },
            body: JSON.stringify(webhookData)
        });

        if (!response.ok) {
            throw new Error(`N8N webhook responded with status: ${response.status}`);
        }

        // Log successful submission
        console.log(`Support ticket submitted successfully: ${user_email} - ${subject} (${priority})`);

        // Return success response
        return res.status(200).json({
            success: true,
            message: via_email_integration 
                ? 'Support ticket sent via your email integration successfully!'
                : 'Support ticket submitted successfully! We\'ll get back to you within 24 hours.',
            timestamp: webhookData.timestamp,
            ticket_id: `ST-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`
        });

    } catch (error) {
        console.error('Support ticket submission error:', error);

        // Return error response
        return res.status(500).json({
            error: 'Internal server error',
            message: 'Failed to process your support request. Please try again later.',
            timestamp: new Date().toISOString()
        });
    }
}

// Vercel Edge Runtime version (alternative export)
export const config = {
    runtime: 'edge',
};

// Edge Runtime handler for Vercel
export async function POST(request) {
    const corsHeaders = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
    };

    try {
        const data = await request.json();
        const { 
            subject, 
            priority, 
            message, 
            user_email, 
            user_name, 
            user_company, 
            via_email_integration 
        } = data;

        // Validate required fields
        if (!subject || !message || !user_email || !user_name) {
            return new Response(JSON.stringify({
                error: 'Missing required fields',
                message: 'Subject, message, user email, and user name are required'
            }), {
                status: 400,
                headers: {
                    ...corsHeaders,
                    'Content-Type': 'application/json',
                },
            });
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(user_email)) {
            return new Response(JSON.stringify({
                error: 'Invalid email format',
                message: 'Please provide a valid email address'
            }), {
                status: 400,
                headers: {
                    ...corsHeaders,
                    'Content-Type': 'application/json',
                },
            });
        }

        // N8N webhook URL for support tickets
        const N8N_SUPPORT_WEBHOOK_URL = process.env.N8N_SUPPORT_WEBHOOK_URL || process.env.N8N_WEBHOOK_URL || 'https://your-n8n-instance.com/webhook/support';

        // Prepare data for N8N
        const webhookData = {
            type: 'support_ticket',
            subject: subject.trim(),
            priority: priority || 'medium',
            message: message.trim(),
            user_name: user_name.trim(),
            user_email: user_email.trim().toLowerCase(),
            user_company: user_company || 'Not specified',
            via_email_integration: via_email_integration || false,
            timestamp: new Date().toISOString(),
            source: 'veritas-agent-dashboard',
            ip: request.headers.get('x-forwarded-for') || request.headers.get('cf-connecting-ip'),
            userAgent: request.headers.get('user-agent')
        };

        // Send to N8N webhook
        const response = await fetch(N8N_SUPPORT_WEBHOOK_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Veritas-Agent-Support/1.0'
            },
            body: JSON.stringify(webhookData)
        });

        if (!response.ok) {
            throw new Error(`N8N webhook responded with status: ${response.status}`);
        }

        // Log successful submission
        console.log(`Support ticket submitted successfully: ${user_email} - ${subject} (${priority})`);

        // Return success response
        return new Response(JSON.stringify({
            success: true,
            message: via_email_integration 
                ? 'Support ticket sent via your email integration successfully!'
                : 'Support ticket submitted successfully! We\'ll get back to you within 24 hours.',
            timestamp: webhookData.timestamp,
            ticket_id: `ST-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`
        }), {
            status: 200,
            headers: {
                ...corsHeaders,
                'Content-Type': 'application/json',
            },
        });

    } catch (error) {
        console.error('Support ticket submission error:', error);

        // Return error response
        return new Response(JSON.stringify({
            error: 'Internal server error',
            message: 'Failed to process your support request. Please try again later.',
            timestamp: new Date().toISOString()
        }), {
            status: 500,
            headers: {
                ...corsHeaders,
                'Content-Type': 'application/json',
            },
        });
    }
}

// Handle OPTIONS requests for CORS
export async function OPTIONS() {
    return new Response(null, {
        status: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
        },
    });
}

-- =====================================================
-- VERITAS AGENT - SUPABASE DATABASE SCHEMA
-- =====================================================
-- Run this SQL in your Supabase SQL Editor to set up the database
--
-- Author: <PERSON>
-- Version: 1.0
-- Date: 2025-06-24
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- 1. CORE TABLES
-- =====================================================

-- User Profiles Table
CREATE TABLE IF NOT EXISTS user_profiles (
    user_id UUID REFERENCES auth.users ON DELETE CASCADE,
    full_name TEXT,
    company_name TEXT,
    role TEXT DEFAULT 'Client' CHECK (role IN ('Owner', 'Client')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (user_id)
);

-- Companies Table
CREATE TABLE IF NOT EXISTS companies (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    user_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI Agents Table
CREATE TABLE IF NOT EXISTS ai_agents (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    company_name TEXT NOT NULL,
    created_by UUID REFERENCES auth.users ON DELETE CASCADE,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending')),
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Integrations Table
CREATE TABLE IF NOT EXISTS user_integrations (
    user_id UUID REFERENCES auth.users ON DELETE CASCADE,
    service_name TEXT NOT NULL,
    status TEXT DEFAULT 'disconnected' CHECK (status IN ('connected', 'disconnected', 'pending')),
    connected_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (user_id, service_name)
);

-- Activity Logs Table
CREATE TABLE IF NOT EXISTS activity_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users ON DELETE CASCADE,
    category TEXT NOT NULL CHECK (category IN ('system', 'integration', 'agents', 'user')),
    type TEXT NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 2. INDEXES FOR PERFORMANCE
-- =====================================================

-- User Profiles Indexes
CREATE INDEX IF NOT EXISTS idx_user_profiles_company_name ON user_profiles(company_name);
CREATE INDEX IF NOT EXISTS idx_user_profiles_role ON user_profiles(role);

-- Companies Indexes
CREATE INDEX IF NOT EXISTS idx_companies_name ON companies(name);
CREATE INDEX IF NOT EXISTS idx_companies_user_count ON companies(user_count);

-- AI Agents Indexes
CREATE INDEX IF NOT EXISTS idx_ai_agents_company_name ON ai_agents(company_name);
CREATE INDEX IF NOT EXISTS idx_ai_agents_created_by ON ai_agents(created_by);
CREATE INDEX IF NOT EXISTS idx_ai_agents_status ON ai_agents(status);

-- User Integrations Indexes
CREATE INDEX IF NOT EXISTS idx_user_integrations_user_id ON user_integrations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_integrations_service_name ON user_integrations(service_name);
CREATE INDEX IF NOT EXISTS idx_user_integrations_status ON user_integrations(status);

-- Activity Logs Indexes
CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_category ON activity_logs(category);
CREATE INDEX IF NOT EXISTS idx_activity_logs_is_read ON activity_logs(is_read);
CREATE INDEX IF NOT EXISTS idx_activity_logs_created_at ON activity_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activity_logs_user_unread ON activity_logs(user_id, is_read) WHERE is_read = FALSE;

-- =====================================================
-- 3. UTILITY FUNCTIONS
-- =====================================================

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to update activity logs updated_at timestamp
CREATE OR REPLACE FUNCTION update_activity_logs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. TRIGGERS FOR AUTOMATIC TIMESTAMP UPDATES
-- =====================================================

-- User Profiles updated_at trigger
DROP TRIGGER IF EXISTS user_profiles_updated_at_trigger ON user_profiles;
CREATE TRIGGER user_profiles_updated_at_trigger
    BEFORE UPDATE ON user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Companies updated_at trigger
DROP TRIGGER IF EXISTS companies_updated_at_trigger ON companies;
CREATE TRIGGER companies_updated_at_trigger
    BEFORE UPDATE ON companies
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- AI Agents updated_at trigger
DROP TRIGGER IF EXISTS ai_agents_updated_at_trigger ON ai_agents;
CREATE TRIGGER ai_agents_updated_at_trigger
    BEFORE UPDATE ON ai_agents
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- User Integrations updated_at trigger
DROP TRIGGER IF EXISTS user_integrations_updated_at_trigger ON user_integrations;
CREATE TRIGGER user_integrations_updated_at_trigger
    BEFORE UPDATE ON user_integrations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Activity Logs updated_at trigger
DROP TRIGGER IF EXISTS activity_logs_updated_at_trigger ON activity_logs;
CREATE TRIGGER activity_logs_updated_at_trigger
    BEFORE UPDATE ON activity_logs
    FOR EACH ROW
    EXECUTE FUNCTION update_activity_logs_updated_at();

-- =====================================================
-- 5. USER REGISTRATION AND COMPANY MANAGEMENT
-- =====================================================

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Wait a moment to ensure user metadata is available
    PERFORM pg_sleep(1);

    -- Insert user profile
    INSERT INTO public.user_profiles (user_id, full_name, company_name)
    VALUES (
        NEW.id,
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'company'
    );

    -- Insert or update company
    INSERT INTO public.companies (name, user_count)
    VALUES (NEW.raw_user_meta_data->>'company', 1)
    ON CONFLICT (name)
    DO UPDATE SET
        user_count = companies.user_count + 1,
        updated_at = NOW();

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- =====================================================
-- 6. ACTIVITY LOGGING FUNCTIONS
-- =====================================================

-- Function to create activity log entries (for manual logging from JavaScript)
CREATE OR REPLACE FUNCTION log_user_activity(
    p_user_id UUID,
    p_category TEXT,
    p_type TEXT,
    p_title TEXT,
    p_message TEXT,
    p_metadata JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    log_id UUID;
BEGIN
    INSERT INTO activity_logs (user_id, category, type, title, message, metadata)
    VALUES (p_user_id, p_category, p_type, p_title, p_message, p_metadata)
    RETURNING id INTO log_id;

    RETURN log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log integration activities (AUTOMATIC TRIGGER)
CREATE OR REPLACE FUNCTION log_integration_activity()
RETURNS TRIGGER AS $$
DECLARE
    activity_title TEXT;
    activity_message TEXT;
    activity_type TEXT;
BEGIN
    -- Determine activity type and message based on the operation
    IF TG_OP = 'INSERT' THEN
        activity_type := 'connected';
        activity_title := 'Integration Connected';
        activity_message := 'Successfully connected ' || NEW.service_name || ' integration';
    ELSIF TG_OP = 'UPDATE' THEN
        IF OLD.status != NEW.status THEN
            IF NEW.status = 'connected' THEN
                activity_type := 'connected';
                activity_title := 'Integration Connected';
                activity_message := 'Successfully connected ' || NEW.service_name || ' integration';
            ELSIF NEW.status = 'disconnected' THEN
                activity_type := 'disconnected';
                activity_title := 'Integration Disconnected';
                activity_message := 'Disconnected ' || NEW.service_name || ' integration';
            ELSE
                activity_type := 'status_changed';
                activity_title := 'Integration Status Changed';
                activity_message := NEW.service_name || ' integration status changed to ' || NEW.status;
            END IF;
        ELSE
            -- No status change, skip logging
            RETURN NEW;
        END IF;
    ELSIF TG_OP = 'DELETE' THEN
        activity_type := 'removed';
        activity_title := 'Integration Removed';
        activity_message := 'Removed ' || OLD.service_name || ' integration';

        -- Log for the deleted record
        INSERT INTO activity_logs (user_id, category, type, title, message, metadata)
        VALUES (
            OLD.user_id,
            'integration',
            activity_type,
            activity_title,
            activity_message,
            jsonb_build_object(
                'service_name', OLD.service_name,
                'previous_status', OLD.status
            )
        );

        RETURN OLD;
    END IF;

    -- Log the activity
    INSERT INTO activity_logs (user_id, category, type, title, message, metadata)
    VALUES (
        NEW.user_id,
        'integration',
        activity_type,
        activity_title,
        activity_message,
        jsonb_build_object(
            'service_name', NEW.service_name,
            'status', NEW.status,
            'previous_status', CASE WHEN TG_OP = 'UPDATE' THEN OLD.status ELSE NULL END
        )
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log AI agent activities (AUTOMATIC TRIGGER)
CREATE OR REPLACE FUNCTION log_agent_activity()
RETURNS TRIGGER AS $$
DECLARE
    activity_title TEXT;
    activity_message TEXT;
    activity_type TEXT;
BEGIN
    -- Determine activity type and message based on the operation
    IF TG_OP = 'INSERT' THEN
        activity_type := 'created';
        activity_title := 'AI Agent Created';
        activity_message := 'Created new AI agent "' || NEW.name || '" for ' || NEW.company_name;
    ELSIF TG_OP = 'UPDATE' THEN
        IF OLD.status != NEW.status THEN
            activity_type := 'status_changed';
            activity_title := 'AI Agent Status Changed';
            activity_message := 'AI agent "' || NEW.name || '" status changed from ' || OLD.status || ' to ' || NEW.status;
        ELSIF OLD.name != NEW.name OR OLD.description != NEW.description THEN
            activity_type := 'updated';
            activity_title := 'AI Agent Updated';
            activity_message := 'Updated AI agent "' || NEW.name || '"';
        ELSE
            -- No significant change, skip logging
            RETURN NEW;
        END IF;
    ELSIF TG_OP = 'DELETE' THEN
        activity_type := 'deleted';
        activity_title := 'AI Agent Deleted';
        activity_message := 'Deleted AI agent "' || OLD.name || '" from ' || OLD.company_name;

        -- Log for the deleted record
        INSERT INTO activity_logs (user_id, category, type, title, message, metadata)
        VALUES (
            OLD.created_by,
            'agents',
            activity_type,
            activity_title,
            activity_message,
            jsonb_build_object(
                'agent_name', OLD.name,
                'company_name', OLD.company_name,
                'previous_status', OLD.status
            )
        );

        RETURN OLD;
    END IF;

    -- Log the activity
    INSERT INTO activity_logs (user_id, category, type, title, message, metadata)
    VALUES (
        NEW.created_by,
        'agents',
        activity_type,
        activity_title,
        activity_message,
        jsonb_build_object(
            'agent_id', NEW.id,
            'agent_name', NEW.name,
            'company_name', NEW.company_name,
            'status', NEW.status,
            'previous_status', CASE WHEN TG_OP = 'UPDATE' THEN OLD.status ELSE NULL END
        )
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
-- =====================================================
-- 7. ACTIVITY LOGGING TRIGGERS
-- =====================================================

-- Create triggers for integration activity logging (AUTOMATIC)
DROP TRIGGER IF EXISTS log_integration_activity_trigger ON user_integrations;
CREATE TRIGGER log_integration_activity_trigger
    AFTER INSERT OR UPDATE OR DELETE ON user_integrations
    FOR EACH ROW
    EXECUTE FUNCTION log_integration_activity();

-- Create triggers for AI agent activity logging (AUTOMATIC)
DROP TRIGGER IF EXISTS log_agent_activity_trigger ON ai_agents;
CREATE TRIGGER log_agent_activity_trigger
    AFTER INSERT OR UPDATE OR DELETE ON ai_agents
    FOR EACH ROW
    EXECUTE FUNCTION log_agent_activity();

-- =====================================================
-- 8. ROW LEVEL SECURITY (RLS) SETUP
-- =====================================================

-- DEMO MODE: Disable RLS for all tables (enable in production)
ALTER TABLE user_profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE companies DISABLE ROW LEVEL SECURITY;
ALTER TABLE ai_agents DISABLE ROW LEVEL SECURITY;
ALTER TABLE user_integrations DISABLE ROW LEVEL SECURITY;
ALTER TABLE activity_logs DISABLE ROW LEVEL SECURITY;

-- =====================================================
-- 9. PERMISSIONS AND GRANTS
-- =====================================================

-- Grant necessary permissions to authenticated users
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- Grant permissions to anon users for public access
GRANT USAGE ON SCHEMA public TO anon;

-- =====================================================
-- SETUP COMPLETE!
-- =====================================================

-- Your Veritas Agent database is now ready!
--
-- Next steps:
-- 1. Update your Supabase URL and API key in public/js/api.js
-- 2. Test user registration and login
-- 3. Verify activity logging is working
-- 4. When ready for production, enable RLS policies
--
-- For support, refer to:
-- - README.md for setup instructions
-- - VERITAS_AGENT_DOCUMENTATION.md for detailed documentation
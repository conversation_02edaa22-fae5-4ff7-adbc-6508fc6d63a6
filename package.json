{"name": "veritas-agent", "version": "1.0.0", "description": "Veritas Agent - Business Automation Agents Platform", "main": "server.js", "scripts": {"dev": "npx http-server public -p 3000 -c-1 -a 127.0.0.1", "start": "node server.js", "start-static": "npx http-server public -p 8080 -a 127.0.0.1", "build": "echo 'No build step required for static site'", "test": "echo 'No tests specified'", "lint": "echo 'No linting configured'", "serve": "npx http-server public -p 3000 -c-1 -o -a 127.0.0.1", "dev-localhost": "npx http-server public -p 3000 -c-1 -a 127.0.0.1 --host localhost", "oauth-server": "node server.js"}, "keywords": ["ai", "automation", "business", "agents", "supabase", "javascript"], "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/veritas-agent.git"}, "homepage": "https://your-domain.com", "devDependencies": {"http-server": "^14.1.1", "nodemon": "^3.0.1"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "engines": {"node": ">=14.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "files": ["public/**/*", "api/**/*", "README.md", "package.json"]}
// Supabase Client Initialization and Data Fetching Functions
// Configuration - Replace these with your actual Supabase project details
const SUPABASE_URL = 'https://iwthujjyuxkeqwqenytj.supabase.co'; // e.g., 'https://your-project-id.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml3dGh1amp5dXhrZXF3cWVueXRqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNzc1NDEsImV4cCI6MjA2NTY1MzU0MX0.LqGPWr81FYNtlpL_ecywGQQTD1q275KFOCm6pgewthc'; // Your Supabase anon/public key

// Real-time data configuration
const REALTIME_UPDATE_INTERVAL = 5000; // 5 seconds
const ENABLE_REALTIME = false; // Set to false to disable real-time features (disabled due to WebSocket issues)
const ENABLE_WEBSOCKET_FALLBACK = false; // Set to false to completely disable WebSocket attempts
let realtimeUpdateInterval;
let websocketConnection = null;

// Real-time subscriptions for dashboard statistics
let statisticsSubscriptions = [];

let supabaseClientInstance = null;

// Store user data including role
let currentUser = {
    id: null,
    email: null,
    fullName: null,
    company: null,
    role: 'Guest'
};

// Error logging function
function logSupabaseError(error, operation = "Unknown operation", component = "Supabase") {
    console.groupCollapsed(`%c🚨 Supabase Error: ${operation} in ${component}`, 'color: #FF6347; font-weight: bold;');
    console.error(`Error details:`, error);
    if (error && error.message) {
        console.error(`Error message: ${error.message}`);
    }
    if (error && error.status) {
        console.error(`Error status: ${error.status}`);
    }
    if (error && error.code) { // Supabase-specific error code
        console.error(`Error code: ${error.code}`);
    }
    console.groupEnd();
}

// Get Supabase client instance
function getSupabaseClient() {
    if (!supabaseClientInstance) {
        if (window.supabase?.createClient) {
            console.log("Initializing Supabase client...");

            // Basic configuration without realtime to avoid WebSocket issues
            const baseConfig = {
                auth: {
                    persistSession: true,
                    autoRefreshToken: true
                }
            };

            // Only add realtime config if enabled and WebSocket fallback is allowed
            if (ENABLE_REALTIME && ENABLE_WEBSOCKET_FALLBACK) {
                console.log("Attempting to initialize with realtime features...");
                try {
                    supabaseClientInstance = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
                        ...baseConfig,
                        realtime: {
                            params: {
                                eventsPerSecond: 10
                            },
                            heartbeatIntervalMs: 30000,
                            reconnectAfterMs: function (tries) {
                                return Math.min(tries * 1000, 30000);
                            }
                        }
                    });

                    // Set up error handling for realtime connection
                    supabaseClientInstance.realtime.onError((error) => {
                        console.warn('Supabase realtime connection error:', error);
                        console.log('Continuing without realtime features...');
                    });

                    console.log("Supabase client initialized with realtime features");
                } catch (error) {
                    console.warn('Error initializing Supabase client with realtime:', error);
                    console.log('Falling back to basic client without realtime...');
                    supabaseClientInstance = null; // Reset to try fallback
                }
            }

            // Fallback to basic client without realtime if realtime failed or is disabled
            if (!supabaseClientInstance) {
                try {
                    supabaseClientInstance = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY, baseConfig);
                    console.log("Supabase client initialized without realtime features");
                } catch (fallbackError) {
                    console.error('Failed to initialize Supabase client:', fallbackError);
                    logSupabaseError(fallbackError, "Client Initialization", "Supabase");
                    return null;
                }
            }
        } else {
            logSupabaseError(new Error("window.supabase object not found. Check CDN script."), "Client Initialization", "Supabase CDN");
            return null;
        }
    }
    return supabaseClientInstance;
}

// Real-time data functions
async function initializeRealtimeUpdates() {
    console.log('Real-time updates disabled to prevent dashboard refresh issues');

    // Only do initial data load, no automatic updates
    // Update statistics once on load
    await updateStatistics();

    // Do not set up any intervals or polling
    console.log('Skipping automatic refresh setup - manual refresh only');
}

// Set up real-time subscriptions for dashboard statistics
async function setupStatisticsSubscriptions() {
    console.log('Setting up real-time statistics subscriptions...');

    const supabase = getSupabaseClient();
    if (!supabase) {
        console.warn('Supabase client not available for real-time subscriptions');
        setupPollingFallback();
        return;
    }

    // Clean up existing subscriptions
    cleanupStatisticsSubscriptions();

    try {
        // Check if real-time is available and connected
        let realtimeStatus = false;
        try {
            realtimeStatus = supabase.realtime.isConnected();
            console.log('Supabase realtime connection status:', realtimeStatus);
        } catch (realtimeError) {
            console.warn('Realtime connection check failed:', realtimeError);
            console.log('Falling back to polling for statistics updates');
            setupPollingFallback();
            return;
        }

        // If realtime is not connected, try to connect with timeout
        if (!realtimeStatus) {
            console.log('Attempting to connect to realtime...');
            try {
                // Set a timeout for connection attempt
                const connectionTimeout = setTimeout(() => {
                    console.warn('Realtime connection timeout, falling back to polling');
                    setupPollingFallback();
                }, 5000);

                supabase.realtime.connect();

                // Wait a bit for connection to establish
                await new Promise(resolve => setTimeout(resolve, 2000));

                clearTimeout(connectionTimeout);

                // Check connection again
                if (!supabase.realtime.isConnected()) {
                    console.warn('Realtime connection failed, using polling fallback');
                    setupPollingFallback();
                    return;
                }
            } catch (connectionError) {
                console.warn('Failed to establish realtime connection:', connectionError);
                setupPollingFallback();
                return;
            }
        }

        // Subscribe to companies table changes with error handling
        const companiesSubscription = supabase
            .channel('companies-changes')
            .on('postgres_changes',
                { event: '*', schema: 'public', table: 'companies' },
                (payload) => {
                    console.log('Companies table changed:', payload);
                    updateStatistics();
                }
            )
            .on('subscribe', (status) => {
                console.log('Companies subscription status:', status);
            })
            .on('error', (error) => {
                console.error('Companies subscription error:', error);
                setupPollingFallback();
            })
            .subscribe();

        // Subscribe to ai_agents table changes with error handling
        const agentsSubscription = supabase
            .channel('ai-agents-changes')
            .on('postgres_changes',
                { event: '*', schema: 'public', table: 'ai_agents' },
                (payload) => {
                    console.log('AI agents table changed:', payload);
                    updateStatistics();
                }
            )
            .on('subscribe', (status) => {
                console.log('AI agents subscription status:', status);
            })
            .on('error', (error) => {
                console.error('AI agents subscription error:', error);
                setupPollingFallback();
            })
            .subscribe();

        // Subscribe to user_profiles table changes with error handling
        const usersSubscription = supabase
            .channel('user-profiles-changes')
            .on('postgres_changes',
                { event: '*', schema: 'public', table: 'user_profiles' },
                (payload) => {
                    console.log('User profiles table changed:', payload);
                    updateStatistics();
                }
            )
            .on('subscribe', (status) => {
                console.log('User profiles subscription status:', status);
            })
            .on('error', (error) => {
                console.error('User profiles subscription error:', error);
                setupPollingFallback();
            })
            .subscribe();

        // Store subscriptions for cleanup
        statisticsSubscriptions = [
            companiesSubscription,
            agentsSubscription,
            usersSubscription
        ];

        console.log('Real-time statistics subscriptions established');

        // Set up a timeout to fallback to polling if subscriptions don't work
        setTimeout(() => {
            const hasActiveSubscriptions = statisticsSubscriptions.some(sub =>
                sub && sub.state === 'joined'
            );

            if (!hasActiveSubscriptions) {
                console.warn('Real-time subscriptions not active, falling back to polling');
                setupPollingFallback();
            }
        }, 10000); // 10 second timeout

    } catch (error) {
        console.error('Error setting up statistics subscriptions:', error);
        console.log('Falling back to polling for statistics updates');
        setupPollingFallback();
    }
}

// Fallback to polling when real-time fails
function setupPollingFallback() {
    console.log('Polling fallback disabled to prevent dashboard refresh issues');

    // Clear any existing polling interval
    if (window.statisticsInterval) {
        clearInterval(window.statisticsInterval);
        window.statisticsInterval = null;
    }

    // Do not set up any polling intervals
    console.log('Automatic polling disabled - use manual refresh buttons only');
}

// Clean up statistics subscriptions
function cleanupStatisticsSubscriptions() {
    console.log('Cleaning up statistics subscriptions...');

    statisticsSubscriptions.forEach(subscription => {
        if (subscription && typeof subscription.unsubscribe === 'function') {
            subscription.unsubscribe();
        }
    });

    statisticsSubscriptions = [];

    // Also clean up any existing polling interval
    if (window.statisticsInterval) {
        clearInterval(window.statisticsInterval);
        window.statisticsInterval = null;
    }
}

// Simulate real-time data updates (DISABLED)
function updateRealtimeData() {
    // Real-time data updates disabled to prevent dashboard refresh issues
    console.log('Real-time data updates disabled');
    return;
}

// Update dashboard metrics with new data
function updateDashboardMetrics(data) {
    const { activeAgents, tasksProcessed, timeSaved, criticalAlerts } = data;
    
    // Helper function to animate number changes
    const animateNumberChange = (element, newValue, duration = 800) => {
        if (!element) return;
        
        const start = parseFloat(element.textContent.replace(/,/g, '')) || 0;
        const range = newValue - start;
        const startTime = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const easeInOutQuad = progress < 0.5 
                ? 2 * progress * progress 
                : 1 - Math.pow(-2 * progress + 2, 2) / 2;
                
            const currentValue = Math.floor(start + range * easeInOutQuad);
            element.textContent = currentValue.toLocaleString();
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                element.textContent = newValue.toLocaleString();
            }
        };
        
        requestAnimationFrame(animate);
    };
    
    // Update active agents with animation
    const agentsElement = document.querySelector('#active-agents-count');
    if (agentsElement) {
        animateNumberChange(agentsElement, activeAgents);
        const pulseElement = agentsElement.closest('div[class*="bg-gradient"]').querySelector('.h-3');
        if (pulseElement) {
            const isActive = activeAgents > 0;
            const newColor = isActive ? 'bg-green-500' : 'bg-red-500';
            const shadowColor = isActive ? 'rgba(34, 197, 94, 0.7)' : 'rgba(239, 68, 68, 0.7)';
            
            // Add flash effect on status change
            if (pulseElement.classList.contains('bg-red-500') && isActive) {
                pulseElement.classList.add('animate-ping');
                setTimeout(() => pulseElement.classList.remove('animate-ping'), 1000);
            } else if (pulseElement.classList.contains('bg-green-500') && !isActive) {
                pulseElement.classList.add('animate-ping');
                setTimeout(() => pulseElement.classList.remove('animate-ping'), 1000);
            }
            
            pulseElement.className = `h-3 w-3 rounded-full ${newColor} shadow-[0_0_10px_${shadowColor}] animate-pulse`;
        }
    }
    
    // Update tasks processed with animation
    const tasksElement = document.querySelector('#tasks-processed-count');
    if (tasksElement) {
        animateNumberChange(tasksElement, tasksProcessed);
        
        // Add a subtle scale animation on update
        tasksElement.classList.add('inline-block', 'transform', 'scale-110');
        setTimeout(() => {
            tasksElement.classList.remove('scale-110');
        }, 300);
    }
    
    // Update time saved with animation
    const timeElement = document.querySelector('#time-saved-count');
    if (timeElement) {
        animateNumberChange(timeElement, timeSaved);
    }
    
    // Update critical alerts with animation
    const alertsElement = document.querySelector('#critical-alerts-count');
    if (alertsElement) {
        const previousAlerts = parseInt(alertsElement.textContent) || 0;
        const isNewAlert = criticalAlerts > previousAlerts;
        
        // Animate the number change
        animateNumberChange(alertsElement, criticalAlerts);
        
        const alertCard = alertsElement.closest('div[class*="bg-gradient"]');
        if (alertCard) {
            if (criticalAlerts > 0) {
                // Add attention-grabbing animation for new alerts
                if (isNewAlert) {
                    alertCard.classList.add('ring-4', 'ring-red-500/70', 'animate-pulse');
                    setTimeout(() => {
                        alertCard.classList.remove('ring-4', 'ring-red-500/70', 'animate-pulse');
                        alertCard.classList.add('ring-2', 'ring-red-500/50');
                    }, 2000);
                } else {
                    alertCard.classList.add('ring-2', 'ring-red-500/50');
                }
            } else {
                alertCard.classList.remove('ring-2', 'ring-red-500/50', 'ring-4', 'ring-red-500/70', 'animate-pulse');
            }
            
            // Add shake animation for new critical alerts
            if (isNewAlert) {
                const alertIcon = alertCard.querySelector('div[class*="text-4xl"]');
                if (alertIcon) {
                    alertIcon.classList.add('animate-shake');
                    setTimeout(() => alertIcon.classList.remove('animate-shake'), 1000);
                }
            }
        }
    }
    
    // Update last updated time with fade animation
    const lastUpdatedElement = document.querySelector('#last-updated-time');
    if (lastUpdatedElement) {
        lastUpdatedElement.classList.add('opacity-0', 'transition-opacity', 'duration-300');
        setTimeout(() => {
            lastUpdatedElement.textContent = `Last updated: ${new Date().toLocaleTimeString()}`;
            lastUpdatedElement.classList.remove('opacity-0');
        }, 150);
    }
    
    // Add animation for new alerts
    const style = document.createElement('style');
    style.textContent = `
        @keyframes pulse-once {
            0% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(239, 68, 68, 0); }
            100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0); }
        }
        .animate-pulse-once {
            animation: pulse-once 1.5s ease-out;
        }
        
        /* Animation for stat changes */
        @keyframes statUpdate {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); color: #00BFFF; }
            100% { transform: scale(1); }
        }
        .stat-updated {
            animation: statUpdate 0.5s ease-in-out;
        }
    `;
    document.head.appendChild(style);

    // Clean up style element after animation is complete
    setTimeout(() => {
        if (style.parentNode === document.head) {
            document.head.removeChild(style);
        }
    }, 2000);
}

// Function to update statistics with real data from database
async function updateStatistics() {
    try {
        // Fetch real statistics data
        const stats = await fetchDashboardStatistics();

        // Update companies count
        const companiesElement = document.getElementById('companies-stat');
        if (companiesElement && stats.companiesCount !== undefined) {
            companiesElement.textContent = stats.companiesCount.toLocaleString();
            companiesElement.classList.add('stat-updated');
            setTimeout(() => companiesElement.classList.remove('stat-updated'), 500);
        }

        // Update active agents count
        const activeAgentsElement = document.getElementById('active-agents-stat');
        if (activeAgentsElement && stats.activeAgentsCount !== undefined) {
            activeAgentsElement.textContent = stats.activeAgentsCount.toLocaleString();
            activeAgentsElement.classList.add('stat-updated');
            setTimeout(() => activeAgentsElement.classList.remove('stat-updated'), 500);
        }

        // Update total users count
        const totalUsersElement = document.getElementById('total-users-stat');
        if (totalUsersElement && stats.totalUsersCount !== undefined) {
            totalUsersElement.textContent = stats.totalUsersCount.toLocaleString();
            totalUsersElement.classList.add('stat-updated');
            setTimeout(() => totalUsersElement.classList.remove('stat-updated'), 500);
        }



    } catch (error) {
        console.error('Error updating statistics:', error);
        // Fallback to showing loading state
        ['companies-stat', 'active-agents-stat', 'total-users-stat'].forEach(id => {
            const element = document.getElementById(id);
            if (element && element.textContent === '-') {
                element.textContent = '0';
            }
        });
    }
}

// Function to fetch dashboard statistics from Supabase
async function fetchDashboardStatistics() {
    console.log('Fetching dashboard statistics...');
    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Supabase client not available');
        }

        // Fetch companies count
        const { count: companiesCount, error: companiesError } = await supabase
            .from('companies')
            .select('*', { count: 'exact', head: true });

        if (companiesError) {
            console.error('Error fetching companies count:', companiesError);
        }

        // Fetch active agents count (status = 'active')
        const { count: activeAgentsCount, error: agentsError } = await supabase
            .from('ai_agents')
            .select('*', { count: 'exact', head: true })
            .eq('status', 'active');

        if (agentsError) {
            console.error('Error fetching active agents count:', agentsError);
        }

        // Fetch total users count
        const { count: totalUsersCount, error: usersError } = await supabase
            .from('user_profiles')
            .select('*', { count: 'exact', head: true });

        if (usersError) {
            console.error('Error fetching total users count:', usersError);
        }

        const stats = {
            companiesCount: companiesCount || 0,
            activeAgentsCount: activeAgentsCount || 0,
            totalUsersCount: totalUsersCount || 0
        };

        console.log('Dashboard statistics fetched:', stats);
        return stats;

    } catch (error) {
        console.error('Error in fetchDashboardStatistics:', error);
        return {
            companiesCount: 0,
            activeAgentsCount: 0,
            totalUsersCount: 0
        };
    }
}

// Function to load companies from Supabase
async function loadCompanies() {
    console.log('loadCompanies function called');
    try {
        const supabase = getSupabaseClient();
        console.log('Supabase client:', supabase);
        if (!supabase) {
            throw new Error('Supabase client not available');
        }

        // Check if user is authenticated
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        console.log('Current session:', session, 'session error:', sessionError);

        console.log('Making query to companies table...');
        const { data, error } = await supabase
            .from('companies')
            .select('id, name, user_count')
            .order('name');

        console.log('Query result - data:', data, 'error:', error);

        if (error) {
            throw error;
        }

        return data || [];
    } catch (error) {
        console.error('Error loading companies:', error);
        logSupabaseError(error, "Load Companies", "API");
        return [];
    }
}

// Function to populate company cards
async function populateCompanyCards() {
    console.log('populateCompanyCards called');

    const container = document.getElementById('company-cards-container');
    const loadingElement = document.getElementById('companies-loading');

    if (!container) {
        console.warn('Company cards container not found');
        return;
    }

    console.log('Company cards container found, loading companies...');

    try {
        const companies = await loadCompanies();
        console.log('Companies loaded for cards:', companies);

        // Hide loading state
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        // Clear any existing cards (except loading)
        const existingCards = container.querySelectorAll('.company-card');
        existingCards.forEach(card => card.remove());

        if (companies.length === 0) {
            // Show empty state
            const emptyState = document.createElement('div');
            emptyState.className = 'flex items-center justify-center p-6 bg-[#1A1A1A] border border-[#333] rounded-lg';
            emptyState.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-building text-3xl text-gray-500 mb-2"></i>
                    <p class="text-gray-400">No companies found</p>
                    <p class="text-sm text-gray-500">Companies will appear here when available</p>
                </div>
            `;
            container.appendChild(emptyState);
            return;
        }

        // Create company cards
        companies.forEach((company, index) => {
            console.log(`Creating card for company ${index + 1}:`, company);

            const card = document.createElement('div');
            card.className = 'company-card cursor-pointer p-4 bg-[#1A1A1A] border-2 border-[#333] rounded-lg hover:border-[#00BFFF] hover:bg-[#222] transition-all duration-200';
            card.dataset.companyName = company.name;

            card.innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-full bg-[#00BFFF]/10 flex items-center justify-center">
                            <i class="fas fa-building text-[#00BFFF]"></i>
                        </div>
                        <div>
                            <h3 class="text-white font-medium">${company.name}</h3>
                            <p class="text-sm text-gray-400">${company.user_count} user${company.user_count !== 1 ? 's' : ''}</p>
                        </div>
                    </div>
                    <div class="company-card-check hidden">
                        <i class="fas fa-check-circle text-[#00BFFF] text-xl"></i>
                    </div>
                </div>
            `;

            // Add click handler
            card.addEventListener('click', () => {
                console.log('Company card clicked:', company.name);
                selectCompanyCard(company.name, card);
            });

            container.appendChild(card);
        });

        console.log('Company cards created successfully!');

    } catch (error) {
        console.error('Error populating company cards:', error);

        // Hide loading state
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        // Show error state
        const errorState = document.createElement('div');
        errorState.className = 'flex items-center justify-center p-6 bg-red-900/20 border border-red-700 rounded-lg';
        errorState.innerHTML = `
            <div class="text-center">
                <i class="fas fa-exclamation-triangle text-3xl text-red-400 mb-2"></i>
                <p class="text-red-300 mb-2">Error loading companies</p>
                <button onclick="populateCompanyCards()" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md text-sm transition-colors">
                    <i class="fas fa-sync-alt mr-2"></i>Retry
                </button>
            </div>
        `;
        container.appendChild(errorState);
    }
}

// Function to select a company card
function selectCompanyCard(companyName, cardElement) {
    console.log('Company selected:', companyName);

    // Update hidden input
    const hiddenInput = document.getElementById('selected-company');
    if (hiddenInput) {
        hiddenInput.value = companyName;
        console.log('Hidden input value set to:', hiddenInput.value);

        // Trigger change event to ensure form validation updates
        hiddenInput.dispatchEvent(new Event('change', { bubbles: true }));
        hiddenInput.dispatchEvent(new Event('input', { bubbles: true }));
    } else {
        console.error('Hidden company input not found!');
    }

    // Update visual selection
    const allCards = document.querySelectorAll('.company-card');
    allCards.forEach(card => {
        card.classList.remove('border-[#00BFFF]', 'bg-[#00BFFF]/10');
        card.classList.add('border-[#333]');
        const checkIcon = card.querySelector('.company-card-check');
        if (checkIcon) {
            checkIcon.classList.add('hidden');
        }
    });

    // Highlight selected card
    cardElement.classList.remove('border-[#333]');
    cardElement.classList.add('border-[#00BFFF]', 'bg-[#00BFFF]/10');
    const checkIcon = cardElement.querySelector('.company-card-check');
    if (checkIcon) {
        checkIcon.classList.remove('hidden');
    }

    // Show success feedback
    if (typeof showToast === 'function') {
        showToast(`Selected: ${companyName}`, 'success');
    }

    // Clear any existing form errors related to company selection
    const form = document.getElementById('ai-agent-form');
    if (form) {
        const existingError = form.querySelector('.form-error-message');
        if (existingError && existingError.textContent.includes('Company')) {
            existingError.remove();
        }

        // Remove error styling from inputs
        const inputs = form.querySelectorAll('.form-error');
        inputs.forEach(input => input.classList.remove('form-error'));
    }
}

// Test function to check Supabase connection
async function testSupabaseConnection() {
    console.log('Testing Supabase connection...');
    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            console.error('Supabase client not available');
            return false;
        }

        // Test basic connection
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        console.log('Session test:', { session: !!session, error: sessionError });

        // Test companies table access
        const { data, error } = await supabase
            .from('companies')
            .select('count(*)', { count: 'exact' });

        console.log('Companies table test:', { data, error });

        if (error) {
            console.error('Companies table access error:', error);
            return false;
        }

        console.log('Supabase connection test successful');
        return true;
    } catch (error) {
        console.error('Supabase connection test failed:', error);
        return false;
    }
}

// Debug function to manually test company cards
async function debugCompanyCards() {
    console.log('=== DEBUGGING COMPANY CARDS ===');

    // Check if container exists
    const container = document.getElementById('company-cards-container');
    console.log('Cards container element:', container);

    if (!container) {
        console.error('Cards container not found! Make sure you are on the AI Agent Management page.');
        return;
    }

    // Test loading companies
    console.log('Testing loadCompanies...');
    const companies = await loadCompanies();
    console.log('Companies result:', companies);

    // Test populating cards
    console.log('Testing populateCompanyCards...');
    await populateCompanyCards();

    // Check final state
    console.log('Final container HTML:', container.innerHTML);
    console.log('Final cards count:', container.querySelectorAll('.company-card').length);

    console.log('=== DEBUG COMPLETE ===');
}

// Function to load AI agents from Supabase
async function loadAIAgents(companyName = null) {
    console.log('loadAIAgents called with company:', companyName);
    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Supabase client not available');
        }

        let query = supabase
            .from('ai_agents')
            .select('id, name, company_name, status, description, created_at, created_by')
            .order('created_at', { ascending: false });

        // Filter by company if specified
        if (companyName) {
            query = query.eq('company_name', companyName);
        }

        const { data, error } = await query;

        console.log('AI Agents query result - data:', data, 'error:', error);

        if (error) {
            throw error;
        }

        return data || [];
    } catch (error) {
        console.error('Error loading AI agents:', error);
        logSupabaseError(error, "Load AI Agents", "API");
        return [];
    }
}

// Function to create AI agent
async function createAIAgent(agentData) {
    console.log('createAIAgent called with data:', agentData);
    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Supabase client not available');
        }

        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError || !user) {
            throw new Error('User authentication required');
        }

        const { data, error } = await supabase
            .from('ai_agents')
            .insert({
                name: agentData.name,
                company_name: agentData.company_name,
                created_by: user.id,
                status: agentData.status || 'active',
                description: agentData.description || `AI Agent for ${agentData.company_name}`
            })
            .select()
            .single();

        if (error) {
            console.error('Create AI agent error:', error);
            throw error;
        }

        console.log('AI Agent created successfully:', data);

        // Trigger immediate statistics update
        triggerStatisticsUpdate();

        return data;
    } catch (error) {
        console.error('Error creating AI agent:', error);
        logSupabaseError(error, "Create AI Agent", "API");
        throw error;
    }
}

// Function to delete AI agent
async function deleteAIAgent(agentId) {
    console.log('deleteAIAgent called with ID:', agentId);
    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Supabase client not available');
        }

        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError || !user) {
            throw new Error('User authentication required');
        }

        // First, get the agent to verify ownership
        const { data: agent, error: fetchError } = await supabase
            .from('ai_agents')
            .select('id, name, created_by')
            .eq('id', agentId)
            .single();

        if (fetchError) {
            console.error('Error fetching agent for deletion:', fetchError);
            throw new Error('Agent not found');
        }

        // Verify user owns this agent
        if (agent.created_by !== user.id) {
            throw new Error('You can only delete agents you created');
        }

        // Delete the agent
        const { error: deleteError } = await supabase
            .from('ai_agents')
            .delete()
            .eq('id', agentId);

        if (deleteError) {
            console.error('Delete AI agent error:', deleteError);
            throw deleteError;
        }

        console.log('AI Agent deleted successfully:', agent.name);

        // Trigger immediate statistics update
        triggerStatisticsUpdate();

        return agent;
    } catch (error) {
        console.error('Error deleting AI agent:', error);
        logSupabaseError(error, "Delete AI Agent", "API");
        throw error;
    }
}

// Function to populate AI agents list
async function populateAIAgentsList() {
    console.log('populateAIAgentsList called');

    const container = document.getElementById('ai-agents-container');
    const loadingElement = document.getElementById('agents-loading');

    if (!container) {
        console.warn('AI agents container not found');
        return;
    }

    console.log('AI agents container found, loading agents...');

    try {
        const agents = await loadAIAgents();
        console.log('AI agents loaded:', agents);

        // Hide loading state
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        // Clear ALL existing content (agent cards, empty state, error state, etc.)
        // Keep only the loading element
        console.log('Clearing existing content. Current children count:', container.children.length);
        const childrenToRemove = Array.from(container.children).filter(child => child.id !== 'agents-loading');
        console.log('Removing', childrenToRemove.length, 'elements');
        childrenToRemove.forEach(child => {
            console.log('Removing element:', child.className, child.textContent?.substring(0, 50));
            child.remove();
        });
        console.log('After clearing, children count:', container.children.length);

        if (agents.length === 0) {
            // Show empty state
            const emptyState = document.createElement('div');
            emptyState.className = 'flex items-center justify-center p-8 bg-[#1A1A1A] border border-[#333] rounded-lg';
            emptyState.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-robot text-4xl text-gray-500 mb-3"></i>
                    <h4 class="text-lg font-medium text-white mb-2">No AI Agents Yet</h4>
                    <p class="text-gray-400 mb-4">Create your first AI agent using the form above</p>
                </div>
            `;
            container.appendChild(emptyState);
            return;
        }

        // Create agent cards
        agents.forEach((agent, index) => {
            console.log(`Creating card for agent ${index + 1}:`, agent);

            const card = document.createElement('div');
            card.className = 'agent-card p-4 bg-[#1A1A1A] border border-[#333] rounded-lg hover:border-[#00BFFF]/50 transition-all duration-200 mb-3';

            const createdDate = new Date(agent.created_at).toLocaleDateString();
            const statusColor = agent.status === 'active' ? 'text-green-400' : 'text-gray-400';
            const statusIcon = agent.status === 'active' ? 'fa-check-circle' : 'fa-pause-circle';

            card.innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 rounded-full bg-[#00BFFF]/10 flex items-center justify-center">
                            <i class="fas fa-robot text-[#00BFFF] text-lg"></i>
                        </div>
                        <div>
                            <h4 class="text-white font-medium text-lg">${agent.name}</h4>
                            <p class="text-sm text-gray-400">${agent.company_name}</p>
                            <p class="text-xs text-gray-500">Created ${createdDate}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="text-right">
                            <div class="flex items-center space-x-2">
                                <i class="fas ${statusIcon} ${statusColor}"></i>
                                <span class="text-sm ${statusColor} capitalize">${agent.status}</span>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button class="agent-edit-btn px-3 py-1 bg-[#333] hover:bg-[#444] text-white rounded text-sm transition-colors"
                                    data-agent-id="${agent.id}" title="Edit agent">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="agent-delete-btn px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition-colors"
                                    data-agent-id="${agent.id}" title="Delete agent">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                ${agent.description ? `<p class="text-sm text-gray-300 mt-3 pl-16">${agent.description}</p>` : ''}
            `;

            // Add event listeners for action buttons
            const editBtn = card.querySelector('.agent-edit-btn');
            const deleteBtn = card.querySelector('.agent-delete-btn');

            if (editBtn) {
                editBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    console.log('Edit agent clicked:', agent.id);
                    // TODO: Implement edit functionality
                    showToast('Edit functionality coming soon!', 'info');
                });
            }

            if (deleteBtn) {
                deleteBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    console.log('Delete agent clicked:', agent.id);
                    handleDeleteAgent(agent.id, agent.name, agent.company_name);
                });
            }

            container.appendChild(card);
        });

        console.log('AI agent cards created successfully!');

    } catch (error) {
        console.error('Error populating AI agents list:', error);

        // Hide loading state
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        // Clear any existing content before showing error
        Array.from(container.children).forEach(child => {
            if (child.id !== 'agents-loading') {
                child.remove();
            }
        });

        // Show error state
        const errorState = document.createElement('div');
        errorState.className = 'flex items-center justify-center p-6 bg-red-900/20 border border-red-700 rounded-lg';
        errorState.innerHTML = `
            <div class="text-center">
                <i class="fas fa-exclamation-triangle text-3xl text-red-400 mb-2"></i>
                <p class="text-red-300 mb-2">Error loading AI agents</p>
                <button onclick="populateAIAgentsList()" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md text-sm transition-colors">
                    <i class="fas fa-sync-alt mr-2"></i>Retry
                </button>
            </div>
        `;
        container.appendChild(errorState);
    }
}

// Function to handle agent deletion with confirmation
function handleDeleteAgent(agentId, agentName, companyName) {
    console.log('handleDeleteAgent called for:', agentId, agentName, companyName);

    // Show confirmation modal
    if (typeof showDeleteAgentModal === 'function') {
        showDeleteAgentModal(agentId, agentName, companyName);
    } else {
        console.error('showDeleteAgentModal function not available');
        // Fallback to basic confirm dialog
        const confirmed = confirm(`Are you sure you want to delete the AI agent "${agentName}"?\n\nThis action cannot be undone.`);

        if (confirmed) {
            performAgentDeletion(agentId, agentName);
        }
    }
}

// Function to perform the actual deletion
async function performAgentDeletion(agentId, agentName) {
    try {
        // Show loading state
        showToast('Deleting agent...', 'info');

        // Delete the agent
        const deletedAgent = await deleteAIAgent(agentId);

        // Show success message
        showToast(`AI Agent "${deletedAgent.name}" deleted successfully!`, 'success');

        // Refresh the agents list
        if (typeof populateAIAgentsList === 'function') {
            await populateAIAgentsList();
        }

    } catch (error) {
        console.error('Error deleting agent:', error);
        showToast(`Failed to delete agent: ${error.message}`, 'error');
    }
}

// Function to clean up all real-time connections
function cleanupRealtimeConnections() {
    console.log('Cleaning up all real-time connections...');

    // Clean up statistics subscriptions
    cleanupStatisticsSubscriptions();

    // Clean up polling intervals
    if (window.realtimeUpdateInterval) {
        clearInterval(window.realtimeUpdateInterval);
        window.realtimeUpdateInterval = null;
    }

    if (window.statisticsInterval) {
        clearInterval(window.statisticsInterval);
        window.statisticsInterval = null;
    }

    if (window.websocketConnection) {
        try {
            window.websocketConnection.close();
        } catch (error) {
            console.log('Error closing websocket connection:', error);
        }
        window.websocketConnection = null;
    }
}

// Function to manually trigger statistics update (for immediate updates after actions)
function triggerStatisticsUpdate() {
    console.log('Manually triggering statistics update...');
    updateStatistics();
}

// Set up cleanup on page unload
window.addEventListener('beforeunload', cleanupRealtimeConnections);

// Make functions available globally for debugging
window.testSupabaseConnection = testSupabaseConnection;
window.loadCompanies = loadCompanies;
window.populateCompanyCards = populateCompanyCards;
window.selectCompanyCard = selectCompanyCard;
window.debugCompanyCards = debugCompanyCards;
window.loadAIAgents = loadAIAgents;
window.createAIAgent = createAIAgent;
window.deleteAIAgent = deleteAIAgent;
window.handleDeleteAgent = handleDeleteAgent;
window.performAgentDeletion = performAgentDeletion;
window.populateAIAgentsList = populateAIAgentsList;
window.cleanupRealtimeConnections = cleanupRealtimeConnections;
window.triggerStatisticsUpdate = triggerStatisticsUpdate;

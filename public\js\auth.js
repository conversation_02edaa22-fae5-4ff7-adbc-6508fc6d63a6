// Authentication Logic and Form Event Listeners

// Global authentication state
let isLoggedIn = false;
let isProfileLoading = false;
let profileLoadingTimeout = null;

// Function to reset profile loading state (for debugging/recovery)
function resetProfileLoadingState() {
    console.log('Manually resetting profile loading state');
    isProfileLoading = false;

    if (profileLoadingTimeout) {
        clearTimeout(profileLoadingTimeout);
        profileLoadingTimeout = null;
    }

    if (typeof setProfileButtonLoading === 'function') {
        setProfileButtonLoading(false);
    }

    if (typeof refreshNavigationProfile === 'function') {
        refreshNavigationProfile();
    }
}

// Make the reset function globally available for debugging
window.resetProfileLoadingState = resetProfileLoadingState;

// Emergency logout function for debugging/recovery
function forceLogout() {
    console.log('Force logout initiated...');

    // Reset all flags
    isLoggingOut = false;
    isProfileLoading = false;
    isLoggedIn = false;

    // Clear timeouts
    if (profileLoadingTimeout) {
        clearTimeout(profileLoadingTimeout);
        profileLoadingTimeout = null;
    }

    // Navigate to home with smooth transition
    showSection('#home', true);

    // Clear user data and update navigation after transition completes
    setTimeout(() => {
        clearUserData();
        updateNavForLoginState();
    }, 600);

    // Reset logout buttons
    const logoutButtons = [
        document.getElementById('navLogoutBtn'),
        document.getElementById('mobile-logout-btn')
    ].filter(btn => btn);

    logoutButtons.forEach(btn => {
        btn.disabled = false;
        btn.innerHTML = btn.innerHTML.includes('mobile')
            ? '<i class="fas fa-sign-out-alt mr-2"></i>Logout'
            : '<i class="mr-2 fas fa-sign-out-alt"></i>Logout';
    });

    console.log('Force logout completed - no page refresh needed');
}

// Make force logout globally available for debugging
window.forceLogout = forceLogout;

// Function to check if logout is safe to proceed
function isLogoutSafe() {
    // Check if already logging out
    if (isLoggingOut) {
        console.log('Logout not safe: already in progress');
        return false;
    }

    // Check if critical operations are in progress
    if (window.isAppInitializing) {
        console.log('Logout not safe: app is initializing');
        return false;
    }

    return true;
}

// Function to ensure authentication state consistency
function ensureAuthStateConsistency() {
    console.log('Checking authentication state consistency...');

    const supabase = getSupabaseClient();
    if (!supabase) {
        console.warn('Supabase client not available for auth state check');
        return;
    }

    // Get current session from Supabase
    supabase.auth.getSession().then(({ data: { session }, error }) => {
        if (error) {
            console.error('Error checking session:', error);
            return;
        }

        const hasSession = !!session;
        const globalStateLoggedIn = !!isLoggedIn;
        const hasCurrentUser = !!(currentUser && currentUser.id);

        console.log('Auth state check:', {
            hasSession,
            globalStateLoggedIn,
            hasCurrentUser,
            sessionUserId: session?.user?.id,
            currentUserId: currentUser?.id
        });

        // If states are inconsistent, fix them
        if (hasSession && (!globalStateLoggedIn || !hasCurrentUser)) {
            console.log('Fixing inconsistent auth state - session exists but global state is wrong');
            isLoggedIn = true;
            if (!hasCurrentUser) {
                currentUser = {
                    id: session.user.id,
                    email: session.user.email,
                    fullName: session.user.user_metadata?.full_name || session.user.email,
                    company: session.user.user_metadata?.company || '',
                    role: 'Client', // SECURITY: Always default to Client role
                    user_metadata: session.user.user_metadata || {}
                };
            }
            updateNavForLoginState();
            // SECURITY: Load profile to get correct role and company status
            loadUserProfile(true);
        } else if (!hasSession && (globalStateLoggedIn || hasCurrentUser)) {
            console.log('Fixing inconsistent auth state - no session but global state thinks user is logged in');
            clearUserData();
            updateNavForLoginState();
        }
    }).catch(error => {
        console.error('Error in auth state consistency check:', error);
    });
}

// Make the consistency check function globally available
window.ensureAuthStateConsistency = ensureAuthStateConsistency;

// Debug function to check current authentication state
function debugAuthState() {
    console.log('=== Authentication State Debug ===');
    console.log('isLoggedIn:', isLoggedIn);
    console.log('currentUser:', currentUser);
    console.log('isProfileLoading:', isProfileLoading);
    console.log('profileLoadingTimeout:', profileLoadingTimeout);

    const supabase = getSupabaseClient();
    if (supabase) {
        supabase.auth.getSession().then(({ data: { session }, error }) => {
            console.log('Supabase session:', session);
            console.log('Session error:', error);
        });
    } else {
        console.log('Supabase client not available');
    }

    // Check UI elements
    const logoutBtn = document.getElementById('navLogoutBtn');
    const mobileLogoutBtn = document.getElementById('mobile-logout-btn');
    const userMenuButton = document.getElementById('userMenuButton');

    console.log('UI Elements:');
    console.log('- navLogoutBtn:', !!logoutBtn, logoutBtn?.disabled);
    console.log('- mobile-logout-btn:', !!mobileLogoutBtn, mobileLogoutBtn?.disabled);
    console.log('- userMenuButton:', !!userMenuButton);

    console.log('=== End Debug ===');
}

// Make debug function globally available
window.debugAuthState = debugAuthState;

// Debug function specifically for logout button issues
function debugLogoutButton() {
    console.log('=== Logout Button Debug ===');

    const logoutBtn = document.getElementById('navLogoutBtn');
    const mobileLogoutBtn = document.getElementById('mobile-logout-btn');
    const profileDropdown = document.getElementById('profileDropdown');

    console.log('Logout button elements:');
    console.log('- navLogoutBtn exists:', !!logoutBtn);
    if (logoutBtn) {
        console.log('  - disabled:', logoutBtn.disabled);
        console.log('  - innerHTML:', logoutBtn.innerHTML);
        console.log('  - event listeners:', getEventListeners ? getEventListeners(logoutBtn) : 'DevTools required');
    }

    console.log('- mobile-logout-btn exists:', !!mobileLogoutBtn);
    if (mobileLogoutBtn) {
        console.log('  - disabled:', mobileLogoutBtn.disabled);
        console.log('  - innerHTML:', mobileLogoutBtn.innerHTML);
    }

    console.log('- profileDropdown exists:', !!profileDropdown);
    if (profileDropdown) {
        console.log('  - hidden:', profileDropdown.classList.contains('hidden'));
        console.log('  - classes:', profileDropdown.className);
    }

    console.log('Global state:');
    console.log('- isLoggedIn:', isLoggedIn);
    console.log('- isProfileLoading:', isProfileLoading);
    console.log('- document.hidden:', document.hidden);

    console.log('=== End Logout Debug ===');
}

// Make logout debug function globally available
window.debugLogoutButton = debugLogoutButton;

// Function to set up auth state change listener
function setupAuthStateListener(supabase) {
    // Clean up previous listener if it exists
    if (window.supabaseAuthListener) {
        // Check if it's a direct subscription with unsubscribe method
        if (typeof window.supabaseAuthListener.unsubscribe === 'function') {
            window.supabaseAuthListener.unsubscribe();
        }
        // Check if it's an object with a subscription in data property
        else if (window.supabaseAuthListener.data &&
                typeof window.supabaseAuthListener.data.unsubscribe === 'function') {
            window.supabaseAuthListener.data.unsubscribe();
        }
    }

    // Set up new auth state change listener
    const authListener = supabase.auth.onAuthStateChange(async (event, session) => {
        console.log('Auth state changed:', event, session);

        // Prevent handling auth events during app initialization to avoid race conditions
        if (window.isAppInitializing) {
            console.log('Skipping auth state change during app initialization');
            return;
        }

        // Prevent handling auth events during logout to avoid conflicts
        if (isLoggingOut && event === 'SIGNED_OUT') {
            console.log('Skipping SIGNED_OUT event during logout process');
            return;
        }

        if (event === 'SIGNED_OUT') {
            console.log('Handling SIGNED_OUT event');

            // Clear any profile loading states
            isProfileLoading = false;
            if (profileLoadingTimeout) {
                clearTimeout(profileLoadingTimeout);
                profileLoadingTimeout = null;
            }

            // Start smooth transition to home
            showSection('#home', true);

            // Clear state after transition completes
            setTimeout(() => {
                isLoggedIn = false;
                clearUserData();
                updateNavForLoginState();
            }, 600);

            // Reset logout buttons if they exist
            const logoutButtons = [
                document.getElementById('navLogoutBtn'),
                document.getElementById('mobile-logout-btn')
            ].filter(btn => btn);

            logoutButtons.forEach(btn => {
                btn.disabled = false;
                btn.innerHTML = btn.innerHTML.includes('mobile')
                    ? '<i class="fas fa-sign-out-alt mr-2"></i>Logout'
                    : '<i class="mr-2 fas fa-sign-out-alt"></i>Logout';
            });

            console.log('Logout completed successfully');

        } else if (event === 'SIGNED_IN') {
            // Check if this is just a tab focus change or actual new sign-in
            const previousUserId = currentUser?.id;
            const newUserId = session.user.id;
            const wasAlreadyLoggedIn = isLoggedIn && previousUserId === newUserId;

            if (wasAlreadyLoggedIn) {
                console.log('Tab focus change detected, ignoring SIGNED_IN event for same user');
                return; // Skip processing this event entirely
            }

            if (previousUserId && previousUserId !== newUserId) {
                console.log('Different user signed in, clearing previous session data...');
                clearDashboardData();
            } else {
                console.log('New user session started...');
            }

            // Update global state first
            isLoggedIn = true;
            currentUser = {
                id: session.user.id,
                email: session.user.email,
                fullName: session.user.user_metadata?.full_name || session.user.email,
                company: session.user.user_metadata?.company || '',
                role: 'Client', // SECURITY: Always start with Client role, will be updated by loadUserProfile
                user_metadata: session.user.user_metadata || {}
            };

            // Update navigation
            updateNavForLoginState();

            // Load profile - only force refresh if this is a different user
            if (previousUserId && previousUserId !== newUserId) {
                console.log('Loading profile for new user session...');
                await loadUserProfile(true);
            } else {
                console.log('Loading profile for first-time user session...');
                await loadUserProfile(false);
            }
        } else if (event === 'PASSWORD_RECOVERY') {
            console.log('Password recovery event detected, redirecting to reset password page');
            // Show the reset password page when user clicks the reset link from email
            showSection('#reset-password-page');
            // Focus on the password input field after the section transition
            setTimeout(() => {
                const passwordInput = document.querySelector('#reset-password-page input[type="password"]');
                if (passwordInput) {
                    passwordInput.focus();
                }
            }, 600);
        } else if (event === 'USER_UPDATED') {
            // For user updates, only refresh if we don't already have current profile data
            console.log('User updated, checking if profile refresh needed...');
            if (!currentUser || !window.cachedUserProfile || forceRefresh) {
                console.log('Profile refresh needed for user update');
                await loadUserProfile(true);
            } else {
                console.log('Profile data already current, skipping refresh');
            }
        } else if (event === 'USER_DELETED') {
            clearUserData();
        }
    });

    // Store the subscription for cleanup
    window.supabaseAuthListener = authListener;
}

// Function to load and display user profile information
async function loadUserProfile(forceRefresh = false) {
    console.log('loadUserProfile called with forceRefresh:', forceRefresh);

    // Reset navigation profile refresh flag at start of profile load
    window.navigationProfileRefreshed = false;

    // Don't proceed if logout is in progress
    if (isLoggingOut) {
        console.log('Logout in progress, skipping profile load');
        return;
    }

    // Don't proceed if no user is logged in
    if (!isLoggedIn || !currentUser) {
        console.log('No user logged in, clearing user data');
        clearUserData();
        return;
    }

    // Prevent multiple simultaneous loads with a more robust check
    if (isProfileLoading) {
        console.log('Profile loading already in progress, waiting for completion...');
        // Wait for the current loading to complete instead of just returning
        let attempts = 0;
        const maxAttempts = 100; // 10 seconds max wait
        while (isProfileLoading && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }

        if (isProfileLoading) {
            console.warn('Profile loading took too long, forcing reset');
            isProfileLoading = false;
            if (profileLoadingTimeout) {
                clearTimeout(profileLoadingTimeout);
                profileLoadingTimeout = null;
            }
        } else {
            console.log('Profile loading completed while waiting');
            return;
        }
    }

    console.log('Starting profile loading process');
    isProfileLoading = true;

    // Set a timeout to prevent infinite loading (reduced to 15 seconds)
    profileLoadingTimeout = setTimeout(() => {
        console.error('Profile loading timeout - forcing reset');
        isProfileLoading = false;
        profileLoadingTimeout = null;

        if (typeof setProfileButtonLoading === 'function') {
            setProfileButtonLoading(false);
        }
        if (typeof refreshNavigationProfile === 'function') {
            refreshNavigationProfile();
        }

        // Show error message to user
        showToast('Profile loading timed out. Please refresh the page.', 'error');
    }, 15000);

    // Show loading state in profile button if function exists
    if (typeof setProfileButtonLoading === 'function') {
        setProfileButtonLoading(true);
    }
    
    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Supabase client not available');
        }

        // Get current user
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError) throw userError;
        if (!user) throw new Error('No user logged in');

        // Get user profile from user_profiles table
        const { data: profileData, error: profileError } = await supabase
            .from('user_profiles')
            .select('*')
            .eq('user_id', user.id)
            .single();

        if (profileError && profileError.code !== 'PGRST116') {
            throw profileError;
        }

        // Check if user needs to create a company
        if (!profileData?.company_name || profileData.company_name === 'Pending' || profileData.company_name === null) {
            // User hasn't created a company yet, redirect to company creation
            console.log('User needs to complete company creation, redirecting...');
            showSection('#company-creation-page');
            showToast('Please create your company to continue.', 'info');
            return;
        }

        // Create user profile data
        const userProfile = {
            fullName: profileData?.full_name || user.user_metadata?.full_name || 'User',
            email: user.email || 'No email',
            company: profileData?.company_name || 'Default Company',
            createdAt: formatDate(profileData?.created_at || user.created_at || new Date().toISOString()),
            // SECURITY: Always default to 'Client' role, never assign Owner without explicit database confirmation
            role: (profileData?.role === 'Owner' || profileData?.role === 'Client') ? profileData.role : 'Client'
        };

        // Store the profile data in the currentUser object
        currentUser.role = userProfile.role;
        currentUser.company = userProfile.company;
        currentUser.fullName = userProfile.fullName;

        // Update the UI with the profile data
        updateProfileUI(userProfile);

        // Refresh the navigation profile display only if not already done
        if (typeof refreshNavigationProfile === 'function' && !window.navigationProfileRefreshed) {
            refreshNavigationProfile();
            window.navigationProfileRefreshed = true;
        }

    } catch (error) {
        console.error('Error loading user profile:', error);
        // Show error state
        const errorElements = [
            'user-full-name',
            'user-email',
            'user-company',
            'profile-full-name',
            'profile-email',
            'profile-company',
            'profile-role',
            'account-created'
        ];

        errorElements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = 'Error loading data';
                element.classList.add('text-red-500');
            }
        });
    } finally {
        // Clear the timeout
        if (profileLoadingTimeout) {
            clearTimeout(profileLoadingTimeout);
            profileLoadingTimeout = null;
        }

        isProfileLoading = false;
        // Remove loading state from profile button if function exists
        if (typeof setProfileButtonLoading === 'function') {
            setProfileButtonLoading(false);
        }

        // Ensure navigation profile is refreshed even if there were errors, but avoid duplicates
        if (typeof refreshNavigationProfile === 'function' && !window.navigationProfileRefreshed) {
            try {
                refreshNavigationProfile();
                window.navigationProfileRefreshed = true;
            } catch (refreshError) {
                console.error('Error refreshing navigation profile:', refreshError);
                // Force rebuild navigation as fallback
                if (typeof updateNavForLoginState === 'function') {
                    updateNavForLoginState();
                }
            }
        }
    }
}

// Update profile UI with user data
function updateProfileUI(userProfile) {
    // Cache the user profile globally for use across the application
    window.cachedUserProfile = userProfile;

    // Update dashboard welcome message
    updateDashboardWelcomeMessage(userProfile.fullName);

    // Update profile elements
    const profileElements = {
        'user-full-name': userProfile.fullName,
        'user-email': userProfile.email,
        'user-company': userProfile.company,
        'profile-full-name': userProfile.fullName,
        'profile-email': userProfile.email,
        'profile-company': userProfile.company,
        'profile-role': userProfile.role,
        'account-created': userProfile.createdAt
    };

    Object.entries(profileElements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
            element.classList.remove('text-red-500'); // Remove error styling
        }
    });

    // Update dashboard menu based on user role
    updateDashboardMenuForRole(userProfile.role);
}

// Update dashboard welcome message
function updateDashboardWelcomeMessage(userName = null) {
    const welcomeTitle = document.getElementById('dashboard-welcome-title');
    if (welcomeTitle) {
        if (userName) {
            welcomeTitle.textContent = `Welcome back, ${userName.split(' ')[0]}!`;
        } else {
            welcomeTitle.textContent = 'Welcome Back!';
        }
    }
}

// Function to update dashboard menu based on user role
function updateDashboardMenuForRole(role) {
    const aiAgentManagementLink = document.querySelector('a[data-view-id="dashboard-ai-agent-management-view"]');
    const companyManagementLink = document.querySelector('a[data-view-id="dashboard-company-management-view"]');
    const clientManagementLink = document.querySelector('a[data-view-id="dashboard-client-management-view"]');
    const integrationsLink = document.querySelector('a[data-view-id="dashboard-integrations-view"]');
    const helpSupportLink = document.querySelector('a[data-view-id="dashboard-help-support-view"]');
    const notificationsLink = document.querySelector('a[data-view-id="dashboard-notifications-view"]');

    console.log('Updating dashboard menu for role:', role);

    if (role === 'Client') {
        // Client role: Show Notifications, Integrations and Help & Support, hide Company Management, hide AI Agent Management, hide Client Management
        if (aiAgentManagementLink) {
            aiAgentManagementLink.style.display = 'none';
        }
        if (companyManagementLink) {
            companyManagementLink.style.display = 'none';
        }
        if (clientManagementLink) {
            clientManagementLink.style.display = 'none';
        }
        if (integrationsLink) {
            integrationsLink.style.display = 'flex';
        }
        if (helpSupportLink) {
            helpSupportLink.style.display = 'flex';
        }
        if (notificationsLink) {
            notificationsLink.style.display = 'flex';
        }
    } else if (role === 'Owner') {
        // Owner role: Hide Notifications, Integrations and Help & Support, show Company Management, show AI Agent Management, show Client Management
        if (aiAgentManagementLink) {
            aiAgentManagementLink.style.display = 'flex';
        }
        if (companyManagementLink) {
            companyManagementLink.style.display = 'flex';
        }
        if (clientManagementLink) {
            clientManagementLink.style.display = 'flex';
        }
        if (integrationsLink) {
            integrationsLink.style.display = 'none';
        }
        if (helpSupportLink) {
            helpSupportLink.style.display = 'none';
        }
        if (notificationsLink) {
            notificationsLink.style.display = 'none';
        }
    } else {
        // Default: Show all features (for development/testing)
        if (aiAgentManagementLink) {
            aiAgentManagementLink.style.display = 'flex';
        }
        if (companyManagementLink) {
            companyManagementLink.style.display = 'flex';
        }
        if (integrationsLink) {
            integrationsLink.style.display = 'flex';
        }
        if (helpSupportLink) {
            helpSupportLink.style.display = 'flex';
        }
    }
}

// Login form handler
async function handleLogin(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const email = formData.get('email');
    const password = formData.get('password');
    
    // Clear any existing errors
    clearFormErrors(form);
    
    // Validate form
    const errors = validateForm({ email, password }, {
        email: { required: true, type: 'email', label: 'Email' },
        password: { required: true, label: 'Password' }
    });
    
    if (errors.length > 0) {
        showFormError(form, errors[0]);
        return;
    }
    
    const submitButton = form.querySelector('button[type="submit"]');
    setLoadingState(submitButton, true);
    
    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Authentication service unavailable');
        }
        
        const { data, error } = await supabase.auth.signInWithPassword({
            email: email,
            password: password
        });
        
        if (error) {
            throw error;
        }
        
        // Only clear dashboard data if logging in as a different user
        const previousUserId = currentUser?.id;
        const newUserId = data.user?.id;

        if (previousUserId && previousUserId !== newUserId) {
            console.log('Different user logging in, clearing previous session data...');
            clearDashboardData();
        } else {
            console.log('Same user logging in, preserving dashboard data...');
        }

        // Show dashboard loading overlay
        showDashboardLoading();

        // Step 1: Authentication completed
        setDashboardLoadingStep('auth', 'completed');
        updateDashboardLoadingProgress(25, 'Authentication Successful', 'Verifying credentials...');

        // Log user login activity
        if (typeof logUserActivity === 'function') {
            await logUserActivity('login', 'User Login', 'Successfully logged into the system', { login_time: new Date().toISOString() });
        }

        // Update global state
        isLoggedIn = true;
        currentUser = {
            id: data.user.id,
            email: data.user.email,
            fullName: data.user.user_metadata?.full_name || data.user.email,
            company: 'Default Company',
            role: 'Client', // Will be updated when profile loads
            user_metadata: data.user.user_metadata || {}
        };

        // Add welcome notification for new sessions
        if (typeof logSystemActivity === 'function') {
            await logSystemActivity('welcome', 'Welcome Back!', 'Welcome back to Veritas Agent. Check out your latest activity and notifications.', { session_start: new Date().toISOString() });
        }

        // Set up auth state listener
        setupAuthStateListener(supabase);

        // Step 2: Loading profile
        setDashboardLoadingStep('profile', 'active');
        updateDashboardLoadingProgress(50, 'Loading Profile', 'Retrieving your account information...');

        // Update navigation
        updateNavForLoginState();

        // Load user profile (this will handle redirection if company creation is needed)
        await loadUserProfile(true);

        // Step 3: Setting up permissions
        completeDashboardLoadingStep('profile');
        setDashboardLoadingStep('permissions', 'active');
        updateDashboardLoadingProgress(75, 'Setting Up Permissions', 'Configuring your access level...');

        // Brief delay to show permissions step
        await new Promise(resolve => setTimeout(resolve, 500));

        // Step 4: Preparing dashboard
        completeDashboardLoadingStep('permissions');
        setDashboardLoadingStep('dashboard', 'active');
        updateDashboardLoadingProgress(90, 'Preparing Dashboard', 'Almost ready...');

        // Only navigate to dashboard if user has completed company creation
        // (loadUserProfile will redirect to company creation if needed)
        if (currentUser && currentUser.company && currentUser.company !== 'Pending') {
            // Start background initialization during the animation delay
            const initializationPromise = initializeDashboardInBackground();

            // Brief delay before showing dashboard (reduced to account for background init)
            await new Promise(resolve => setTimeout(resolve, 400));

            // Start the page transition
            showSection('#ai-dashboard-page');

            // Wait for background initialization to complete during page transition
            await initializationPromise;

            hideDashboardLoading();
            showToast('Welcome back! Dashboard loaded successfully.', 'success');
        } else {
            // Hide loading overlay if redirecting to company creation
            hideDashboardLoading();
        }
        
    } catch (error) {
        console.error('Login error:', error);

        // Hide dashboard loading overlay on error
        hideDashboardLoading();

        let errorMessage = 'Login failed. Please try again.';

        if (error.message.includes('Invalid login credentials')) {
            errorMessage = 'Invalid email or password.';
        } else if (error.message.includes('Email not confirmed')) {
            errorMessage = 'Please check your email and confirm your account.';
        }

        showFormError(form, errorMessage);
    } finally {
        setLoadingState(submitButton, false);
    }
}



// Check if user exists in database
async function checkUserExists(email) {
    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            return false;
        }

        // Check if user has a profile in the user_profiles table
        const { data, error } = await supabase
            .from('user_profiles')
            .select('id, email, full_name, company_id')
            .eq('email', email)
            .single();

        if (error && error.code !== 'PGRST116') { // PGRST116 is "not found" error
            console.error('Error checking user existence:', error);
            return false;
        }

        return !!data; // Returns true if user exists, false otherwise
    } catch (error) {
        console.error('Error in checkUserExists:', error);
        return false;
    }
}



// Register form handler
async function handleRegister(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const fullName = formData.get('full_name');
    const email = formData.get('email');
    const password = formData.get('password');
    const confirmPassword = formData.get('confirm_password');

    // Clear any existing errors
    clearFormErrors(form);

    // Validate form
    const errors = validateForm({
        full_name: fullName,
        email,
        password,
        confirm_password: confirmPassword
    }, {
        full_name: { required: true, label: 'Full Name' },
        email: { required: true, type: 'email', label: 'Email' },
        password: { required: true, type: 'password', label: 'Password' },
        confirm_password: { required: true, label: 'Confirm Password' }
    });
    
    if (errors.length > 0) {
        showFormError(form, errors[0]);
        return;
    }
    
    // Check password confirmation
    if (password !== confirmPassword) {
        showFormError(form, 'Passwords do not match.');
        return;
    }
    
    const submitButton = form.querySelector('button[type="submit"]');
    setLoadingState(submitButton, true);
    
    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Authentication service unavailable');
        }
        
        const { data, error } = await supabase.auth.signUp({
            email: email,
            password: password,
            options: {
                data: {
                    full_name: fullName
                }
            }
        });
        
        if (error) {
            throw error;
        }
        
        if (data.user && !data.session) {
            // Email confirmation required
            showToast('Please check your email and click the confirmation link to complete registration.', 'info', 5000);
            showSection('#login-page');
        } else if (data.session) {
            // Auto-login successful - show loading and redirect to company creation
            showDashboardLoading();
            setDashboardLoadingStep('auth', 'completed');
            updateDashboardLoadingProgress(25, 'Registration Successful', 'Setting up your account...');

            isLoggedIn = true;
            currentUser = {
                id: data.user.id,
                email: data.user.email,
                fullName: fullName,
                company: null, // No company yet
                role: 'Client',
                user_metadata: data.user.user_metadata || { full_name: fullName }
            };

            setupAuthStateListener(supabase);
            updateNavForLoginState();

            // Step 2: Creating profile
            setDashboardLoadingStep('profile', 'active');
            updateDashboardLoadingProgress(50, 'Creating Profile', 'Setting up your user profile...');

            // Ensure user profile exists (fallback in case trigger failed)
            try {
                const { data: profileCheck, error: profileCheckError } = await supabase
                    .from('user_profiles')
                    .select('user_id')
                    .eq('user_id', data.user.id)
                    .single();

                if (profileCheckError && profileCheckError.code === 'PGRST116') {
                    // Profile doesn't exist, create it
                    const { error: createProfileError } = await supabase
                        .from('user_profiles')
                        .insert([{
                            user_id: data.user.id,
                            full_name: fullName,
                            company_name: 'Pending',
                            role: 'Client'
                        }]);

                    if (createProfileError) {
                        console.error('Error creating user profile:', createProfileError);
                        // Continue anyway, the company creation will handle it
                    }
                }
            } catch (profileError) {
                console.error('Error checking/creating user profile:', profileError);
                // Continue anyway, the company creation will handle it
            }

            completeDashboardLoadingStep('profile');
            updateDashboardLoadingProgress(75, 'Profile Created', 'Preparing company setup...');

            // Brief delay before showing company creation
            await new Promise(resolve => setTimeout(resolve, 800));

            // Redirect to company creation page instead of dashboard
            hideDashboardLoading();
            showSection('#company-creation-page');
            showToast('Registration successful! Please create your company to continue.', 'success');
        }
        
    } catch (error) {
        console.error('Registration error:', error);

        // Hide dashboard loading overlay on error
        hideDashboardLoading();

        let errorMessage = 'Registration failed. Please try again.';

        if (error.message.includes('already registered')) {
            errorMessage = 'An account with this email already exists.';
        } else if (error.message.includes('Password')) {
            errorMessage = 'Password does not meet requirements.';
        }

        showFormError(form, errorMessage);
    } finally {
        setLoadingState(submitButton, false);
    }
}

// Forgot password form handler
async function handleForgotPassword(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const email = formData.get('email');

    // Clear any existing errors
    clearFormErrors(form);

    // Validate form
    const errors = validateForm({ email }, {
        email: { required: true, type: 'email', label: 'Email' }
    });

    if (errors.length > 0) {
        showFormError(form, errors[0]);
        return;
    }

    const submitButton = form.querySelector('button[type="submit"]');
    setLoadingState(submitButton, true);

    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Authentication service unavailable');
        }

        // Get the current site URL for redirect
        const redirectTo = `${window.location.origin}${window.location.pathname}#reset-password`;

        const { error } = await supabase.auth.resetPasswordForEmail(email, {
            redirectTo: redirectTo
        });

        if (error) {
            throw error;
        }

        // Show success message
        showToast('Password reset link sent! Please check your email.', 'success', 5000);

        // Redirect back to login page after a short delay
        setTimeout(() => {
            showSection('#login-page');
        }, 2000);

    } catch (error) {
        console.error('Forgot password error:', error);

        let errorMessage = 'Failed to send reset link. Please try again.';

        if (error.message.includes('not found')) {
            errorMessage = 'No account found with this email address.';
        } else if (error.message.includes('rate limit')) {
            errorMessage = 'Too many requests. Please wait before trying again.';
        }

        showFormError(form, errorMessage);
    } finally {
        setLoadingState(submitButton, false);
    }
}

// Reset password form handler
async function handleResetPassword(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const password = formData.get('password');
    const confirmPassword = formData.get('confirm_password');

    // Clear any existing errors
    clearFormErrors(form);

    // Validate form
    const errors = validateForm({
        password,
        confirm_password: confirmPassword
    }, {
        password: { required: true, type: 'password', label: 'Password' },
        confirm_password: { required: true, label: 'Confirm Password' }
    });

    if (errors.length > 0) {
        showFormError(form, errors[0]);
        return;
    }

    // Check password confirmation
    if (password !== confirmPassword) {
        showFormError(form, 'Passwords do not match.');
        return;
    }

    const submitButton = form.querySelector('button[type="submit"]');
    setLoadingState(submitButton, true);

    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Authentication service unavailable');
        }

        const { error } = await supabase.auth.updateUser({
            password: password
        });

        if (error) {
            throw error;
        }

        // Show success message
        showToast('Password updated successfully!', 'success', 5000);

        // Redirect to dashboard or login
        setTimeout(() => {
            if (isLoggedIn) {
                showSection('#ai-dashboard-page');
            } else {
                showSection('#login-page');
            }
        }, 2000);

    } catch (error) {
        console.error('Reset password error:', error);

        let errorMessage = 'Failed to update password. Please try again.';

        if (error.message.includes('session')) {
            errorMessage = 'Reset link has expired. Please request a new one.';
        } else if (error.message.includes('Password')) {
            errorMessage = 'Password does not meet requirements.';
        }

        showFormError(form, errorMessage);
    } finally {
        setLoadingState(submitButton, false);
    }
}

// Company creation form handler
async function handleCompanyCreation(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const companyName = formData.get('company_name');

    // Clear any existing errors
    clearFormErrors(form);

    // Validate form
    const errors = validateForm({
        company_name: companyName
    }, {
        company_name: { required: true, label: 'Company Name' }
    });

    if (errors.length > 0) {
        showFormError(form, errors[0]);
        return;
    }

    const submitButton = form.querySelector('button[type="submit"]');
    setLoadingState(submitButton, true);

    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Service unavailable');
        }

        // Get current user
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError || !user) {
            throw new Error('User not authenticated');
        }

        // Create company with proper user tracking
        const { data: companyData, error: companyError } = await supabase
            .from('companies')
            .insert([{
                name: companyName,
                created_by: user.id,
                user_count: 1
            }])
            .select()
            .single();

        if (companyError) {
            if (companyError.code === '23505') { // Unique constraint violation
                throw new Error('A company with this name already exists. Please choose a different name.');
            }
            throw companyError;
        }

        console.log('Company created successfully:', companyData);

        // Ensure user profile exists and update with company information
        const { data: existingProfile, error: checkError } = await supabase
            .from('user_profiles')
            .select('user_id')
            .eq('user_id', user.id)
            .single();

        if (checkError && checkError.code !== 'PGRST116') {
            throw checkError;
        }

        if (!existingProfile) {
            // Create user profile if it doesn't exist (fallback)
            const { error: insertError } = await supabase
                .from('user_profiles')
                .insert([{
                    user_id: user.id,
                    full_name: user.user_metadata?.full_name || 'User',
                    company_name: companyName,
                    role: 'Client' // Keep as Client role
                }]);

            if (insertError) {
                throw insertError;
            }
        } else {
            // Update existing user profile with company information
            const { error: profileError } = await supabase
                .from('user_profiles')
                .update({
                    company_name: companyName,
                    // Keep existing role (should be 'Client'), don't change it
                    updated_at: new Date().toISOString()
                })
                .eq('user_id', user.id);

            if (profileError) {
                throw profileError;
            }
        }

        // Update current user object
        currentUser.company = companyName;
        currentUser.role = 'Client'; // Keep as Client role

        // Verify the user profile was updated correctly
        const { data: verifyProfile, error: verifyError } = await supabase
            .from('user_profiles')
            .select('user_id, full_name, company_name, role')
            .eq('user_id', user.id)
            .single();

        if (verifyError) {
            console.error('Error verifying user profile update:', verifyError);
        } else {
            console.log('User profile verified:', verifyProfile);
        }

        // Show dashboard loading overlay
        showDashboardLoading();
        setDashboardLoadingStep('auth', 'completed');
        setDashboardLoadingStep('profile', 'completed');
        setDashboardLoadingStep('permissions', 'active');
        updateDashboardLoadingProgress(75, 'Company Created', 'Setting up your dashboard...');

        // Load user profile to refresh data (only if needed)
        if (!window.cachedUserProfile) {
            await loadUserProfile(true);
        }

        // Step 4: Preparing dashboard
        completeDashboardLoadingStep('permissions');
        setDashboardLoadingStep('dashboard', 'active');
        updateDashboardLoadingProgress(90, 'Preparing Dashboard', 'Almost ready...');

        // Start background initialization during the animation delay
        const initializationPromise = initializeDashboardInBackground();

        // Brief delay before showing dashboard (reduced to account for background init)
        await new Promise(resolve => setTimeout(resolve, 400));

        // Trigger immediate statistics update for new company and user
        if (typeof triggerStatisticsUpdate === 'function') {
            triggerStatisticsUpdate();
        }

        // Start the page transition
        showSection('#ai-dashboard-page');

        // Wait for background initialization to complete during page transition
        await initializationPromise;

        hideDashboardLoading();
        showToast('Company created successfully! Welcome to Veritas Agent.', 'success');

    } catch (error) {
        console.error('Company creation error:', error);

        // Hide dashboard loading overlay on error
        hideDashboardLoading();

        let errorMessage = 'Failed to create company. Please try again.';

        if (error.message.includes('already exists')) {
            errorMessage = error.message;
        } else if (error.message.includes('User not authenticated')) {
            errorMessage = 'Please log in again to continue.';
            // Redirect to login
            showSection('#login-page');
            return;
        }

        showFormError(form, errorMessage);
    } finally {
        setLoadingState(submitButton, false);
    }
}

// Global flag to prevent multiple simultaneous logout attempts
let isLoggingOut = false;

// Logout function - with smooth fade transition
async function logout() {
    // Check if logout is safe to proceed
    if (!isLogoutSafe()) {
        console.log('Logout not safe, ignoring request');
        return;
    }

    isLoggingOut = true;
    console.log('Starting logout process with smooth transition...');

    try {
        // Hide any open dropdowns immediately
        const dropdown = document.getElementById('profileDropdown');
        if (dropdown) {
            dropdown.classList.add('hidden');
        }

        // Clear any profile loading states immediately
        if (isProfileLoading) {
            console.log('Clearing profile loading state during logout');
            isProfileLoading = false;
            if (profileLoadingTimeout) {
                clearTimeout(profileLoadingTimeout);
                profileLoadingTimeout = null;
            }
        }

        // Get current section for smooth transition
        const currentSection = document.querySelector('section.is-visible');
        const homeSection = document.querySelector('#home');

        if (currentSection && homeSection && currentSection !== homeSection) {
            console.log('Starting smooth logout transition...');

            // Start fade out of current section
            currentSection.classList.remove('is-visible');

            // Wait for fade out to complete, then fade in home
            setTimeout(() => {
                // Hide current section
                currentSection.style.display = 'none';
                currentSection.style.visibility = 'hidden';

                // Prepare home section for fade in
                homeSection.style.display = 'flex';
                homeSection.style.visibility = 'visible';

                // Use requestAnimationFrame for smooth fade in
                requestAnimationFrame(() => {
                    requestAnimationFrame(() => {
                        homeSection.classList.add('is-visible');
                    });
                });

                // Clear user data after fade in starts
                setTimeout(() => {
                    console.log('Clearing user data after transition...');
                    isLoggedIn = false;
                    clearUserData();
                    updateNavForLoginState();
                }, 100);

            }, 550); // Wait for fade out to complete
        } else {
            // Fallback to regular showSection if no current section
            showSection('#home', true);
            setTimeout(() => {
                isLoggedIn = false;
                clearUserData();
                updateNavForLoginState();
            }, 600);
        }

        // Background Supabase signout (don't wait for it)
        const supabase = getSupabaseClient();
        if (supabase) {
            // Fire and forget - don't await this
            supabase.auth.signOut().then(({ error }) => {
                if (error) {
                    console.error('Background Supabase logout error:', error);
                    logSupabaseError(error, "Sign Out", "Auth");
                } else {
                    console.log('Background Supabase logout completed successfully');
                }
            }).catch(error => {
                console.error('Background Supabase logout failed:', error);
            });
        } else {
            console.warn('Supabase client not available for background logout');
        }

        console.log('Logout with smooth transition initiated successfully');

    } catch (error) {
        console.error('Error during logout:', error);

        // Even on error, ensure we transition to home and clear state
        isLoggedIn = false;
        clearUserData();
        updateNavForLoginState();
        showSection('#home', true);

        showToast('Logged out successfully (background cleanup may have failed)', 'warning');

    } finally {
        // Reset the logout flag after full transition completes
        setTimeout(() => {
            isLoggingOut = false;
        }, 1200); // Wait for full transition cycle to complete
    }
}

// Enhanced Constellation Animation for Home Section
// Creates an interactive animated node background with mouse interaction

(function() {
    'use strict';

    var canvas = document.getElementById("demo-canvas");
    var mousePos = { x: 0, y: 0 };
    var context = null;
    var timeoutID = null;
    var rafID = null;
    var net = undefined;
    var animateHeader = true;

    // Initialize the constellation animation when DOM is loaded
    function initConstellation() {
        // Only initialize if we're on the home section and canvas exists
        const homeSection = document.getElementById('home');
        canvas = document.getElementById('demo-canvas');

        if (!homeSection || !canvas) {
            return;
        }

        if (canvas.getContext) {
            context = canvas.getContext("2d");
        }

        addListeners();
        init();
    }

    function addListeners() {
        window.addEventListener('mousemove', printMouse, false);
        window.addEventListener('touchmove', printMouse, false);

        window.addEventListener("resize", function() {
            clearTimeout(timeoutID);
            window.cancelAnimationFrame(rafID);
            timeoutID = setTimeout(init, 500);
        }, false);
    }

    // Init
    function init() {
        if (!canvas || !context) return;

        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;

        var nodesLength = Math.floor(canvas.width * canvas.height / 4000); // Reduced density for better performance

        // Nodes
        net = new Net();
        net.populate(nodesLength);

        rafID = window.requestAnimationFrame(render);

        function render(e) {
            // Only animate when home section is visible
            const homeSection = document.getElementById('home');
            if (homeSection && homeSection.classList.contains('is-visible')) {
                net.update();
                net.draw();
                net.connect(120);
                rafID = window.requestAnimationFrame(render);
            } else {
                rafID = window.requestAnimationFrame(render);
            }
        }
    }

    // Net
    class Net {
        constructor() {
            this.nodes = [];
            this.length = undefined;
        }

        populate(length) {
            this.length = length;

            for (var i = 0; i < length; i++) {
                var xPos = Math.floor(getRandom(0, canvas.width));
                var yPos = Math.floor(getRandom(0, canvas.height));
                this.nodes.push(new Node(xPos, yPos));
            }
        }

        update() {
            for (var i = 0; i < this.length; i++) {
                this.nodes[i].update();
            }
        }

        draw() {
            // Use transparent background instead of dark fill
            context.clearRect(0, 0, canvas.width, canvas.height);

            for (var i = 0; i < this.length; i++) {
                this.nodes[i].draw();
            }
        }

        connect(distanceMax) {
            // Loop through all nodes
            for (var i = 0; i < this.length - 1; i++) {
                this.nodes[i].connections = [];

                // Store connected nodes in node object
                for (var j = 0; j < this.length - 1; j++) {
                    var a = this.nodes[j].x - this.nodes[i].x;
                    var b = this.nodes[j].y - this.nodes[i].y;
                    var c = Math.sqrt(a*a + b*b);

                    var xToMouse = this.nodes[j].x - mousePos.x;
                    var yToMouse = this.nodes[j].y - mousePos.y;
                    this.nodes[i].dToMouse = Math.floor(Math.sqrt(xToMouse*xToMouse + yToMouse*yToMouse));

                    var d = distanceMax/this.nodes[i].dToMouse*200;

                    if (distanceMax/this.nodes[i].dToMouse*200 > distanceMax) {
                        d = distanceMax;
                    }

                    if (j > i && c < d) {
                        this.nodes[i].connections.push(j);
                    }
                }

                // Draw line between dots using brand colors
                for (var k = 0; k < this.nodes[i].connections.length; k++) {
                    context.beginPath();
                    context.moveTo(this.nodes[i].x, this.nodes[i].y);
                    context.lineTo(this.nodes[this.nodes[i].connections[k]].x, this.nodes[this.nodes[i].connections[k]].y);
                    // Use brand color with opacity based on depth
                    context.strokeStyle = "rgba(0,191,255,"+ (this.nodes[i].depth/3) +")";
                    context.stroke();
                }
            }
        }
    }

    // Node
    class Node {
        constructor(_x, _y) {
            this.x = _x;
            this.y = _y;
            this.radius = 1.5; // Slightly smaller for subtlety
            this.depth = Math.floor(getRandom(1, 10))/10;
        }

        update() {
            var velocity = (1 - this.depth)/8; // Slower movement
            this.x = this.x + velocity;

            if (this.x > canvas.width || this.x < 0) {
                this.x = 0;
            }
        }

        draw() {
            var alpha = (1 - this.depth) * 0.8; // Reduced opacity for subtlety
            context.beginPath();
            context.arc(this.x, this.y, this.radius, 0, 2 * Math.PI, false);
            // Use brand color instead of white
            context.fillStyle = 'rgba(0,191,255,' + alpha + ')';
            context.fill();
        }
    }

    // Helpers
    function getRandom(min, max) {
        return Math.random() * (max - min) + min;
    }

    // Mouse tracking
    function getMousePos(canvas, evt) {
        var rect = canvas.getBoundingClientRect(),
            scaleX = canvas.width / rect.width,
            scaleY = canvas.height / rect.height;

        return {
            x: (evt.clientX - rect.left) * scaleX,
            y: (evt.clientY - rect.top) * scaleY
        }
    }

    function printMouse(e) {
        if (!canvas) return;
        var pos = getMousePos(canvas, e);
        mousePos = pos;
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initConstellation);
    } else {
        initConstellation();
    }

    // Also initialize when home section becomes visible
    document.addEventListener('sectionChanged', function(e) {
        if (e.detail && e.detail.sectionId === 'home') {
            setTimeout(initConstellation, 100);
        }
    });

})();

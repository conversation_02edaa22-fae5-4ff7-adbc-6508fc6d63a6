// Modal Logic and Interactions

// Capability modal content data
const capabilityModalContent = {
    'software-integration': {
        title: 'Phase 1: Discovery & Seamless Integration',
        content: `
            <div class="modal-intro">
                <p>Our comprehensive discovery process ensures that Veritas Agent integrates seamlessly with your existing software ecosystem, maximizing efficiency while minimizing disruption to your current operations.</p>
            </div>

            <div class="modal-section">
                <h4><span class="section-icon">🔍</span> Discovery Process</h4>
                <div class="process-grid">
                    <div class="process-step-modal">
                        <div class="process-icon">🔍</div>
                        <div class="process-text">
                            <h5>System Analysis</h5>
                            <p>We conduct a comprehensive audit of your current software stack, identifying integration points, data flows, and optimization opportunities across all platforms.</p>
                        </div>
                    </div>

                    <div class="process-step-modal">
                        <div class="process-icon">🔗</div>
                        <div class="process-text">
                            <h5>Integration Mapping</h5>
                            <p>Our experts map optimal integration pathways, ensuring seamless data flow and communication between Veritas Agent and your existing systems.</p>
                        </div>
                    </div>

                    <div class="process-step-modal">
                        <div class="process-icon">⚡</div>
                        <div class="process-text">
                            <h5>Performance Optimization</h5>
                            <p>We identify bottlenecks and optimization opportunities to enhance overall system performance, efficiency, and user experience.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-section">
                <h4><span class="section-icon">🔧</span> Integration Capabilities</h4>
                <div class="capabilities-grid-modal">
                    <div class="capability-category">
                        <h5>CRM & Sales</h5>
                        <ul>
                            <li>Salesforce</li>
                            <li>HubSpot</li>
                            <li>Pipedrive</li>
                            <li>Zoho CRM</li>
                        </ul>
                    </div>
                    <div class="capability-category">
                        <h5>Project Management</h5>
                        <ul>
                            <li>Asana</li>
                            <li>Trello</li>
                            <li>Monday.com</li>
                            <li>Jira</li>
                        </ul>
                    </div>
                    <div class="capability-category">
                        <h5>Communication</h5>
                        <ul>
                            <li>Slack</li>
                            <li>Microsoft Teams</li>
                            <li>Discord</li>
                            <li>Zoom</li>
                        </ul>
                    </div>
                    <div class="capability-category">
                        <h5>Data & Cloud</h5>
                        <ul>
                            <li>AWS Services</li>
                            <li>Azure Platform</li>
                            <li>Google Cloud</li>
                            <li>Custom APIs</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="modal-section">
                <h4><span class="section-icon">✨</span> Key Benefits</h4>
                <div class="benefits-grid">
                    <div class="benefit-item">
                        <div class="benefit-icon">🚀</div>
                        <div class="benefit-text">
                            <h5>Zero Disruption</h5>
                            <p>Seamless integration with minimal impact on existing workflows</p>
                        </div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">🎯</div>
                        <div class="benefit-text">
                            <h5>Data Consistency</h5>
                            <p>Unified data flow across all platforms and systems</p>
                        </div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">📊</div>
                        <div class="benefit-text">
                            <h5>Enhanced Visibility</h5>
                            <p>Complete operational oversight and real-time insights</p>
                        </div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">⚡</div>
                        <div class="benefit-text">
                            <h5>Reduced Errors</h5>
                            <p>Automated processes eliminate manual data entry mistakes</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-cta">
                <p><strong>Ready to transform your business operations?</strong></p>
                <p>Let's discuss how our discovery and integration process can optimize your systems.</p>
            </div>
        `
    },
    'custom-agent-building': {
        title: 'Phase 2: Bespoke AI Agent Development',
        content: `
            <div class="modal-intro">
                <p>Our expert team designs, builds, and rigorously tests custom AI agents tailored precisely to automate your unique business challenges and requirements, delivering solutions that truly understand your business.</p>
            </div>

            <div class="modal-section">
                <h4><span class="section-icon">🏗️</span> Development Process</h4>
                <div class="process-grid">
                    <div class="process-step-modal">
                        <div class="process-icon">🎯</div>
                        <div class="process-text">
                            <h5>Requirements Analysis</h5>
                            <p>Deep collaboration with your team to understand specific automation needs, business logic, and define clear, measurable objectives for your AI agent.</p>
                        </div>
                    </div>

                    <div class="process-step-modal">
                        <div class="process-icon">🏗️</div>
                        <div class="process-text">
                            <h5>Custom Development</h5>
                            <p>Our developers create bespoke AI agents using cutting-edge technologies, tailored to your unique business processes and requirements.</p>
                        </div>
                    </div>

                    <div class="process-step-modal">
                        <div class="process-icon">🧪</div>
                        <div class="process-text">
                            <h5>Rigorous Testing</h5>
                            <p>Comprehensive testing across multiple scenarios ensures your AI agent performs reliably under various conditions and edge cases.</p>
                        </div>
                    </div>

                    <div class="process-step-modal">
                        <div class="process-icon">🚀</div>
                        <div class="process-text">
                            <h5>Deployment & Training</h5>
                            <p>Seamless deployment with comprehensive team training to ensure maximum adoption and utilization of your AI agent's capabilities.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-section">
                <h4><span class="section-icon">🤖</span> AI Agent Capabilities</h4>
                <div class="capabilities-grid-modal">
                    <div class="capability-category">
                        <h5>Communication</h5>
                        <ul>
                            <li>Natural Language Processing</li>
                            <li>Multi-language Support</li>
                            <li>Context-aware Responses</li>
                            <li>Sentiment Analysis</li>
                        </ul>
                    </div>
                    <div class="capability-category">
                        <h5>Data Processing</h5>
                        <ul>
                            <li>Automated Data Analysis</li>
                            <li>Pattern Recognition</li>
                            <li>Predictive Analytics</li>
                            <li>Real-time Processing</li>
                        </ul>
                    </div>
                    <div class="capability-category">
                        <h5>Task Management</h5>
                        <ul>
                            <li>Intelligent Scheduling</li>
                            <li>Priority Management</li>
                            <li>Workflow Automation</li>
                            <li>Resource Optimization</li>
                        </ul>
                    </div>
                    <div class="capability-category">
                        <h5>Decision Making</h5>
                        <ul>
                            <li>Business Rule Engine</li>
                            <li>Risk Assessment</li>
                            <li>Adaptive Learning</li>
                            <li>Performance Monitoring</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="modal-section">
                <h4><span class="section-icon">⚙️</span> Technology Stack</h4>
                <div class="tech-stack">
                    <div class="tech-category">
                        <h5>AI & Machine Learning</h5>
                        <div class="tech-items">
                            <span class="tech-item">GPT-4 Turbo</span>
                            <span class="tech-item">Claude 3</span>
                            <span class="tech-item">Custom Models</span>
                            <span class="tech-item">TensorFlow</span>
                        </div>
                    </div>
                    <div class="tech-category">
                        <h5>Cloud & Infrastructure</h5>
                        <div class="tech-items">
                            <span class="tech-item">AWS</span>
                            <span class="tech-item">Azure</span>
                            <span class="tech-item">Google Cloud</span>
                            <span class="tech-item">Kubernetes</span>
                        </div>
                    </div>
                    <div class="tech-category">
                        <h5>Integration & APIs</h5>
                        <div class="tech-items">
                            <span class="tech-item">REST APIs</span>
                            <span class="tech-item">GraphQL</span>
                            <span class="tech-item">Webhooks</span>
                            <span class="tech-item">Real-time Sync</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-section">
                <h4><span class="section-icon">🎯</span> Key Benefits</h4>
                <div class="benefits-grid">
                    <div class="benefit-item">
                        <div class="benefit-icon">🎨</div>
                        <div class="benefit-text">
                            <h5>Tailored Solutions</h5>
                            <p>Custom-built for your specific business needs and processes</p>
                        </div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">📈</div>
                        <div class="benefit-text">
                            <h5>Scalable Architecture</h5>
                            <p>Grows seamlessly with your business requirements</p>
                        </div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">🕒</div>
                        <div class="benefit-text">
                            <h5>24/7 Operations</h5>
                            <p>Continuous automated operations and support</p>
                        </div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">💰</div>
                        <div class="benefit-text">
                            <h5>Cost Reduction</h5>
                            <p>Significant reduction in operational costs and overhead</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-cta">
                <p><strong>Ready to build your custom AI agent?</strong></p>
                <p>Let's create an intelligent solution that transforms your business operations.</p>
            </div>
        `
    },
    'high-roi-agents': {
        title: 'Phase 3: High ROI Automation & Optimization',
        content: `
            <div class="modal-intro">
                <p>We implement and continuously refine solutions focused on delivering measurable financial returns, driving efficiency, and accelerating business growth through data-driven optimization strategies.</p>
            </div>

            <div class="modal-section">
                <h4><span class="section-icon">📊</span> ROI-Focused Implementation</h4>
                <div class="process-grid">
                    <div class="process-step-modal">
                        <div class="process-icon">📊</div>
                        <div class="process-text">
                            <h5>Baseline Measurement</h5>
                            <p>We establish comprehensive metrics and baselines to accurately measure the impact of automation on your business operations and financial performance.</p>
                        </div>
                    </div>

                    <div class="process-step-modal">
                        <div class="process-icon">🎯</div>
                        <div class="process-text">
                            <h5>Strategic Deployment</h5>
                            <p>Agents are deployed in carefully planned phases, targeting high-impact areas first to maximize immediate returns on investment and minimize risk.</p>
                        </div>
                    </div>

                    <div class="process-step-modal">
                        <div class="process-icon">📈</div>
                        <div class="process-text">
                            <h5>Performance Monitoring</h5>
                            <p>Advanced monitoring and analytics provide real-time insights into agent performance, business impact, and optimization opportunities.</p>
                        </div>
                    </div>

                    <div class="process-step-modal">
                        <div class="process-icon">🔄</div>
                        <div class="process-text">
                            <h5>Continuous Optimization</h5>
                            <p>Regular refinements, updates, and enhancements ensure your agents continue to deliver increasing value and adapt to changing business needs.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-section">
                <h4><span class="section-icon">💰</span> Measurable Benefits</h4>
                <div class="benefits-grid">
                    <div class="benefit-item">
                        <div class="benefit-icon">💰</div>
                        <div class="benefit-text">
                            <h5>Cost Reduction</h5>
                            <p>40-70% reduction in manual processing costs</p>
                        </div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">⚡</div>
                        <div class="benefit-text">
                            <h5>Time Savings</h5>
                            <p>60-80% faster task completion times</p>
                        </div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">🎯</div>
                        <div class="benefit-text">
                            <h5>Error Reduction</h5>
                            <p>95%+ reduction in human errors</p>
                        </div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">📈</div>
                        <div class="benefit-text">
                            <h5>Scalability</h5>
                            <p>Handle 10x more volume without proportional cost increase</p>
                        </div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">🕒</div>
                        <div class="benefit-text">
                            <h5>24/7 Operations</h5>
                            <p>Round-the-clock processing capabilities</p>
                        </div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">😊</div>
                        <div class="benefit-text">
                            <h5>Customer Satisfaction</h5>
                            <p>Faster response times and consistent service quality</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-section">
                <h4><span class="section-icon">📊</span> ROI Tracking & Reporting</h4>
                <div class="capabilities-grid-modal">
                    <div class="capability-category">
                        <h5>Real-time Analytics</h5>
                        <ul>
                            <li>Live performance dashboards</li>
                            <li>Key performance indicators</li>
                            <li>Cost savings tracking</li>
                            <li>Efficiency metrics</li>
                        </ul>
                    </div>
                    <div class="capability-category">
                        <h5>Detailed Reporting</h5>
                        <ul>
                            <li>Monthly ROI reports</li>
                            <li>Cost-benefit analysis</li>
                            <li>Performance projections</li>
                            <li>Trend analysis</li>
                        </ul>
                    </div>
                    <div class="capability-category">
                        <h5>Strategic Insights</h5>
                        <ul>
                            <li>Optimization recommendations</li>
                            <li>Industry benchmarking</li>
                            <li>Growth opportunities</li>
                            <li>Risk assessments</li>
                        </ul>
                    </div>
                    <div class="capability-category">
                        <h5>Business Intelligence</h5>
                        <ul>
                            <li>Predictive analytics</li>
                            <li>Market insights</li>
                            <li>Competitive analysis</li>
                            <li>Strategic planning</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="modal-section">
                <h4><span class="section-icon">📅</span> Typical ROI Timeline</h4>
                <div class="timeline-container">
                    <div class="timeline-item">
                        <div class="timeline-marker">1-2</div>
                        <div class="timeline-content">
                            <h5>Months 1-2: Foundation</h5>
                            <p>Initial deployment and baseline establishment. Setting up monitoring systems and training teams.</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-marker">3-6</div>
                        <div class="timeline-content">
                            <h5>Months 3-6: Growth</h5>
                            <p>200-400% ROI as agents reach full efficiency and optimization cycles begin showing results.</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-marker">6+</div>
                        <div class="timeline-content">
                            <h5>Month 6+: Optimization</h5>
                            <p>500%+ ROI through continuous optimization, scaling, and advanced feature implementation.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-cta">
                <p><strong>Ready to maximize your ROI with intelligent automation?</strong></p>
                <p>Let's discuss how our optimization strategies can transform your business performance.</p>
            </div>
        `
    }
};

// Show capability modal
function showCapabilityModal(capability) {
    const modal = document.getElementById('capabilityDetailModal');
    const modalTitle = document.getElementById('modalCapabilityTitle');
    const modalBody = document.getElementById('modalCapabilityContent');

    if (!modal || !modalTitle || !modalBody) {
        console.error('Modal elements not found');
        console.error('Modal:', modal);
        console.error('Modal Title:', modalTitle);
        console.error('Modal Body:', modalBody);
        return;
    }

    const content = capabilityModalContent[capability];
    if (!content) {
        console.error('Modal content not found for capability:', capability);
        return;
    }

    // Set modal content
    modalTitle.textContent = content.title;
    modalBody.innerHTML = content.content;

    // Show modal
    modal.style.display = 'flex';

    // Add fade-in animation
    setTimeout(() => {
        modal.classList.add('modal-visible');

        // Always reset scroll to top when opening a new modal
        const modalContent = modal.querySelector('.modal-content');
        if (modalContent) {
            modalContent.scrollTop = 0;
        }
    }, 10);

    // Prevent body scroll
    document.body.style.overflow = 'hidden';
}

// Hide capability modal
function hideCapabilityModal() {
    const modal = document.getElementById('capabilityDetailModal');
    if (!modal) return;

    // Add fade-out animation
    modal.classList.remove('modal-visible');

    // Hide modal after animation
    setTimeout(() => {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }, 300);
}

// Delete Agent Modal Functions
function showDeleteAgentModal(agentId, agentName, companyName) {
    console.log('showDeleteAgentModal called for:', agentId, agentName, companyName);

    const modal = document.getElementById('deleteAgentModal');
    if (!modal) {
        console.error('Delete agent modal not found');
        return;
    }

    // Populate modal with agent details
    const agentNameElement = document.getElementById('delete-agent-name');
    const agentCompanyElement = document.getElementById('delete-agent-company');

    if (agentNameElement) agentNameElement.textContent = agentName;
    if (agentCompanyElement) agentCompanyElement.textContent = companyName;

    // Store agent ID for deletion
    modal.dataset.agentId = agentId;
    modal.dataset.agentName = agentName;

    modal.style.display = 'flex';
    setTimeout(() => {
        modal.classList.add('modal-visible');
    }, 10);
    document.body.style.overflow = 'hidden';
}

function hideDeleteAgentModal() {
    const modal = document.getElementById('deleteAgentModal');
    if (!modal) return;

    modal.classList.remove('modal-visible');
    setTimeout(() => {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';

        // Clear stored data
        delete modal.dataset.agentId;
        delete modal.dataset.agentName;
    }, 300);
}

// Profile Edit Modal Functions
function showProfileEditModal() {
    const modal = document.getElementById('profileEditModal');
    if (!modal) {
        console.error('Profile edit modal not found');
        return;
    }

    // Populate form with current user data
    populateProfileEditForm();

    // Show modal
    modal.style.display = 'flex';
    setTimeout(() => {
        modal.classList.add('modal-visible');
    }, 10);

    document.body.style.overflow = 'hidden';
}

function hideProfileEditModal() {
    const modal = document.getElementById('profileEditModal');
    if (!modal) return;

    // Clear form
    const form = document.getElementById('profileEditForm');
    if (form) {
        form.reset();
        clearProfileEditMessage();
    }

    // Add fade-out animation
    modal.classList.remove('modal-visible');

    // Hide modal after animation
    setTimeout(() => {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }, 300);
}

function populateProfileEditForm() {
    if (!window.currentUser) {
        console.warn('No current user data available for edit form');
        return;
    }

    const formFields = {
        'edit-full-name': window.currentUser.fullName,
        'edit-company': window.currentUser.company,
        'edit-email': window.currentUser.email
    };

    Object.entries(formFields).forEach(([fieldId, value]) => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.value = value || '';
        }
    });
}

function showProfileEditMessage(message, type = 'info') {
    const messageDiv = document.getElementById('profile-edit-message');
    if (!messageDiv) return;

    messageDiv.textContent = message;
    messageDiv.className = `mt-4 p-3 rounded-lg text-sm ${
        type === 'success' ? 'bg-green-900/50 text-green-300 border border-green-700' :
        type === 'error' ? 'bg-red-900/50 text-red-300 border border-red-700' :
        'bg-blue-900/50 text-blue-300 border border-blue-700'
    }`;
    messageDiv.classList.remove('hidden');
}

function clearProfileEditMessage() {
    const messageDiv = document.getElementById('profile-edit-message');
    if (messageDiv) {
        messageDiv.classList.add('hidden');
    }
}

// Handle profile edit form submission
async function handleProfileEditSubmit(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const fullName = formData.get('full_name');
    const company = formData.get('company');
    const email = formData.get('email');
    const newPassword = formData.get('new_password');
    const confirmPassword = formData.get('confirm_password');

    // Clear any existing messages
    clearProfileEditMessage();

    // Validate form
    if (!fullName || !company || !email) {
        showProfileEditMessage('Please fill in all required fields.', 'error');
        return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        showProfileEditMessage('Please enter a valid email address.', 'error');
        return;
    }

    // Validate password if provided
    if (newPassword || confirmPassword) {
        if (newPassword !== confirmPassword) {
            showProfileEditMessage('Passwords do not match.', 'error');
            return;
        }
        if (newPassword.length < 6) {
            showProfileEditMessage('Password must be at least 6 characters long.', 'error');
            return;
        }
    }

    const submitButton = form.querySelector('button[type="submit"]');
    const buttonText = submitButton.querySelector('.button-text');
    const loadingIcon = submitButton.querySelector('.loading-icon');

    // Set loading state
    submitButton.disabled = true;
    buttonText.textContent = 'Saving...';
    loadingIcon.classList.remove('hidden');

    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Authentication service unavailable');
        }

        // Update email if changed
        const currentEmail = window.cachedUserProfile?.email || currentUser?.email;
        if (email !== currentEmail) {
            const { error: emailError } = await supabase.auth.updateUser({
                email: email
            });

            if (emailError) {
                throw emailError;
            }
        }

        // Update password if provided
        if (newPassword) {
            const { error: passwordError } = await supabase.auth.updateUser({
                password: newPassword
            });

            if (passwordError) {
                throw passwordError;
            }
        }

        // Update user metadata
        const { error: metadataError } = await supabase.auth.updateUser({
            data: {
                full_name: fullName,
                company: company
            }
        });

        if (metadataError) {
            throw metadataError;
        }

        // Update profile in database
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
            const { error: profileError } = await supabase
                .from('user_profiles')
                .upsert({
                    user_id: user.id,
                    full_name: fullName,
                    company_name: company,
                    updated_at: new Date().toISOString()
                });

            if (profileError) {
                console.warn('Profile update error:', profileError);
                // Don't throw here as the main update succeeded
            }
        }

        // Update current user object
        if (window.currentUser) {
            window.currentUser.fullName = fullName;
            window.currentUser.email = email;
            window.currentUser.company = company;
        }

        // Update current user object
        if (window.currentUser) {
            window.currentUser.fullName = fullName;
            window.currentUser.email = email;
            window.currentUser.company = company;
            if (window.currentUser.user_metadata) {
                window.currentUser.user_metadata.full_name = fullName;
                window.currentUser.user_metadata.company = company;
            }
        }

        // Update UI
        if (typeof updateProfileUI === 'function' && window.cachedUserProfile) {
            updateProfileUI(window.cachedUserProfile);
        }
        if (typeof refreshNavigationProfile === 'function') {
            refreshNavigationProfile();
        }

        showProfileEditMessage('Profile updated successfully!', 'success');

        // Clear password fields
        const passwordFields = form.querySelectorAll('input[type="password"]');
        passwordFields.forEach(field => field.value = '');

        // Close modal after a short delay
        setTimeout(() => {
            hideProfileEditModal();
            if (typeof showToast === 'function') {
                showToast('Profile updated successfully!', 'success');
            }
        }, 1500);

    } catch (error) {
        console.error('Profile update error:', error);
        let errorMessage = 'Failed to update profile. Please try again.';

        if (error.message.includes('email')) {
            errorMessage = 'Email update failed. Please check your email format.';
        } else if (error.message.includes('password')) {
            errorMessage = 'Password update failed. Please check your password requirements.';
        }

        showProfileEditMessage(errorMessage, 'error');
    } finally {
        // Reset loading state
        submitButton.disabled = false;
        buttonText.textContent = 'Save Changes';
        loadingIcon.classList.add('hidden');
    }
}

// Generic modal functions
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (!modal) {
        console.error(`Modal with ID ${modalId} not found`);
        return;
    }

    modal.style.display = 'flex';
    setTimeout(() => {
        modal.classList.add('modal-visible');
    }, 10);

    document.body.style.overflow = 'hidden';
}

function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (!modal) return;
    
    modal.classList.remove('modal-visible');
    setTimeout(() => {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }, 300);
}

// Setup modal event listeners
function setupModalEventListeners() {
    // Delete Agent modal setup
    const deleteAgentModal = document.getElementById('deleteAgentModal');
    if (deleteAgentModal) {
        // Close buttons
        const closeButtons = deleteAgentModal.querySelectorAll('.modal-close');
        closeButtons.forEach(btn => {
            btn.addEventListener('click', hideDeleteAgentModal);
        });

        // Confirm delete button
        const confirmDeleteBtn = document.getElementById('confirm-delete-agent');
        if (confirmDeleteBtn) {
            confirmDeleteBtn.addEventListener('click', async () => {
                const agentId = deleteAgentModal.dataset.agentId;
                const agentName = deleteAgentModal.dataset.agentName;

                if (agentId && typeof deleteAIAgent === 'function') {
                    hideDeleteAgentModal();

                    try {
                        showToast('Deleting agent...', 'info');
                        const deletedAgent = await deleteAIAgent(agentId);
                        showToast(`AI Agent "${deletedAgent.name}" deleted successfully!`, 'success');

                        // Refresh the agents list
                        if (typeof populateAIAgentsList === 'function') {
                            await populateAIAgentsList();
                        }
                    } catch (error) {
                        console.error('Error deleting agent:', error);
                        showToast(`Failed to delete agent: ${error.message}`, 'error');
                    }
                }
            });
        }

        // Click outside to close
        deleteAgentModal.addEventListener('click', (event) => {
            if (event.target === deleteAgentModal) {
                hideDeleteAgentModal();
            }
        });

        // Escape key to close
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && deleteAgentModal.style.display === 'flex') {
                hideDeleteAgentModal();
            }
        });
    }

    // Capability modal setup
    const capabilityModal = document.getElementById('capabilityDetailModal');
    if (capabilityModal) {
        // Close button
        const closeBtn = capabilityModal.querySelector('.modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', hideCapabilityModal);
        }

        // Click outside to close
        capabilityModal.addEventListener('click', (event) => {
            if (event.target === capabilityModal) {
                hideCapabilityModal();
            }
        });

        // Escape key to close
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && capabilityModal.style.display === 'flex') {
                hideCapabilityModal();
            }
        });
    }

    // Profile edit modal setup
    const profileEditModal = document.getElementById('profileEditModal');
    if (profileEditModal) {
        // Close buttons
        const closeButtons = profileEditModal.querySelectorAll('.modal-close');
        closeButtons.forEach(btn => {
            btn.addEventListener('click', hideProfileEditModal);
        });

        // Click outside to close
        profileEditModal.addEventListener('click', (event) => {
            if (event.target === profileEditModal) {
                hideProfileEditModal();
            }
        });

        // Escape key to close
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && profileEditModal.style.display === 'flex') {
                hideProfileEditModal();
            }
        });

        // Form submission
        const profileEditForm = document.getElementById('profileEditForm');
        if (profileEditForm) {
            profileEditForm.addEventListener('submit', handleProfileEditSubmit);
        }
    }
    
    // Capability card click handlers
    const capabilityCards = document.querySelectorAll('.capability-card[data-capability]');
    capabilityCards.forEach(card => {
        // Add click handler to the entire card
        card.addEventListener('click', () => {
            const capability = card.getAttribute('data-capability');
            if (capability) {
                showCapabilityModal(capability);
            }
        });

        // Add click handler specifically to the learn more button
        const learnMoreBtn = card.querySelector('.learn-more-btn');
        if (learnMoreBtn) {
            learnMoreBtn.addEventListener('click', (e) => {
                e.stopPropagation(); // Prevent double triggering
                const capability = card.getAttribute('data-capability');
                if (capability) {
                    showCapabilityModal(capability);
                }
            });
        }
    });
}

// Profile menu hover behavior - hover only, no click toggle
function setupProfileMenuHover() {
    const userMenuButton = document.getElementById('userMenuButton');
    const profileDropdown = document.getElementById('profileDropdown');

    if (!userMenuButton || !profileDropdown) {
        return;
    }

    // Clean up any existing event listeners to prevent duplicates
    if (window.profileMenuCleanup) {
        window.profileMenuCleanup();
    }

    // Add dropdown-menu class for animations
    profileDropdown.classList.add('dropdown-menu');
    // Remove hidden class and ensure dropdown starts hidden
    profileDropdown.classList.remove('hidden');
    profileDropdown.classList.remove('show'); // Ensure it starts hidden

    let hoverTimeout;
    let isAnimating = false;

    // Show dropdown on hover with animation
    const showDropdown = () => {
        clearTimeout(hoverTimeout);
        if (isAnimating) return;

        isAnimating = true;
        profileDropdown.classList.add('show');

        // Reset animation flag after transition
        setTimeout(() => {
            isAnimating = false;
        }, 300);
    };

    // Hide dropdown when leaving both button and dropdown with animation
    const hideDropdown = () => {
        hoverTimeout = setTimeout(() => {
            if (isAnimating) return;

            isAnimating = true;
            profileDropdown.classList.remove('show');

            // Reset animation flag after transition
            setTimeout(() => {
                isAnimating = false;
            }, 300);
        }, 150); // Small delay to allow moving to dropdown
    };

    // Keep dropdown open when hovering over it
    const keepDropdownOpen = () => {
        clearTimeout(hoverTimeout);
    };

    // Prevent default click behavior on the button (no click toggle)
    const preventClick = (e) => {
        e.preventDefault();
        e.stopPropagation();
    };

    // Add event listeners - only hover, no click toggle
    userMenuButton.addEventListener('mouseenter', showDropdown);
    userMenuButton.addEventListener('mouseleave', hideDropdown);
    userMenuButton.addEventListener('click', preventClick);
    profileDropdown.addEventListener('mouseenter', keepDropdownOpen);
    profileDropdown.addEventListener('mouseleave', hideDropdown);

    // Store cleanup function for later use
    window.profileMenuCleanup = () => {
        userMenuButton.removeEventListener('mouseenter', showDropdown);
        userMenuButton.removeEventListener('mouseleave', hideDropdown);
        userMenuButton.removeEventListener('click', preventClick);
        profileDropdown.removeEventListener('mouseenter', keepDropdownOpen);
        profileDropdown.removeEventListener('mouseleave', hideDropdown);
        clearTimeout(hoverTimeout);
        isAnimating = false;
    };

    // Add visibility change listener to handle tab switching
    const handleVisibilityChange = () => {
        if (document.hidden) {
            // Tab is hidden - clear any hover timeouts and hide dropdown
            clearTimeout(hoverTimeout);
            if (!isAnimating) {
                isAnimating = true;
                profileDropdown.classList.remove('show');
                setTimeout(() => {
                    isAnimating = false;
                }, 300);
            }
        }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Update cleanup function to include visibility change listener
    const originalCleanup = window.profileMenuCleanup;
    window.profileMenuCleanup = () => {
        originalCleanup();
        document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
}

// Loading overlay functions
function showLoadingOverlay() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        // Reset progress
        const progressBar = document.getElementById('loading-bar');
        const statusText = document.getElementById('loading-status');
        if (progressBar) progressBar.style.width = '0%';
        if (statusText) statusText.textContent = 'Initializing...';
        
        // Show overlay with fade in
        overlay.classList.remove('hidden');
        requestAnimationFrame(() => {
            overlay.style.opacity = '1';
        });
    }
}

function updateLoadingProgress(progress, message) {
    const progressBar = document.getElementById('loading-bar');
    const statusText = document.getElementById('loading-status');
    
    if (progressBar) {
        progressBar.style.transition = 'width 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
        progressBar.style.width = `${Math.min(progress, 100)}%`;
    }
    
    if (statusText && message) {
        statusText.textContent = message;
    }
}

async function hideLoadingOverlay() {
    const overlay = document.getElementById('loading-overlay');
    if (!overlay) return;
    
    // Complete the progress bar if not already
    updateLoadingProgress(100, 'Almost there...');
    
    // Wait a moment to show completion
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // Fade out overlay
    overlay.style.opacity = '0';
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Hide overlay after fade out
    overlay.classList.add('hidden');
    
    // Reset for next time
    const progressBar = document.getElementById('loading-bar');
    if (progressBar) progressBar.style.transition = 'none';
}

// Initialize modal functionality
function initializeModals() {
    setupModalEventListeners();

    // Add CSS for modal animations if not already present
    if (!document.querySelector('#modal-animations-style')) {
        const style = document.createElement('style');
        style.id = 'modal-animations-style';
        style.textContent = `
            .modal {
                opacity: 0;
                transition: opacity 0.3s ease-out;
            }

            .modal.modal-visible {
                opacity: 1;
            }

            .modal-content {
                transform: translateY(-20px);
                transition: transform 0.3s ease-out;
            }

            .modal.modal-visible .modal-content {
                transform: translateY(0);
            }
        `;
        document.head.appendChild(style);
    }
}

// Make modal functions globally available
window.showCapabilityModal = showCapabilityModal;
window.hideCapabilityModal = hideCapabilityModal;
window.showDeleteAgentModal = showDeleteAgentModal;
window.hideDeleteAgentModal = hideDeleteAgentModal;
window.showProfileEditModal = showProfileEditModal;
window.hideProfileEditModal = hideProfileEditModal;
window.handleProfileEditSubmit = handleProfileEditSubmit;
window.setupModalEventListeners = setupModalEventListeners;
window.initializeModals = initializeModals;

// Profile Loading Debug Utilities
// This file provides debugging tools for profile loading issues

// Debug function to check profile loading state
function debugProfileState() {
    console.group('🔍 Profile Loading Debug Info');
    console.log('isLoggedIn:', window.isLoggedIn);
    console.log('isProfileLoading:', window.isProfileLoading);
    console.log('currentUser:', window.currentUser);
    
    const userMenuButton = document.getElementById('userMenuButton');
    if (userMenuButton) {
        console.log('Profile button exists:', true);
        console.log('Profile button loading state:', userMenuButton.getAttribute('data-loading'));
        console.log('Profile button classes:', userMenuButton.className);
        console.log('Profile button content:', userMenuButton.innerHTML);
    } else {
        console.log('Profile button exists:', false);
    }
    
    const profileDropdown = document.getElementById('profileDropdown');
    console.log('Profile dropdown exists:', !!profileDropdown);
    
    console.groupEnd();
}

// Function to force profile refresh (disabled in production to prevent constant refreshing)
function forceProfileRefresh() {
    console.log('🔄 Profile refresh disabled to prevent constant refreshing');
    console.log('Use browser dev tools to manually call loadUserProfile(true) if needed');

    // Only allow in development mode
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        console.log('Development mode detected, allowing profile refresh...');

        // Reset loading state first
        if (typeof window.resetProfileLoadingState === 'function') {
            window.resetProfileLoadingState();
        }

        // Wait a moment then reload profile
        setTimeout(() => {
            if (typeof window.loadUserProfile === 'function') {
                window.loadUserProfile(true);
            }
        }, 100);
    }
}

// Function to simulate profile loading timeout (disabled in production)
function simulateProfileTimeout() {
    console.log('⏰ Profile timeout simulation disabled to prevent issues');

    // Only allow in development mode
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        console.log('Development mode detected, allowing timeout simulation...');

        // Set loading state
        window.isProfileLoading = true;
        if (typeof setProfileButtonLoading === 'function') {
            setProfileButtonLoading(true);
        }

        // Wait 5 seconds then check if timeout mechanism works
        setTimeout(() => {
            console.log('Checking if profile is still loading after 5 seconds...');
            debugProfileState();
        }, 5000);
    }
}

// Make debug functions globally available
window.debugProfileState = debugProfileState;
window.forceProfileRefresh = forceProfileRefresh;
window.simulateProfileTimeout = simulateProfileTimeout;

// Auto-debug on page load if in development
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    // Add debug button to page
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            const debugButton = document.createElement('button');
            debugButton.innerHTML = '🔍 Debug Profile';
            debugButton.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                z-index: 9999;
                background: #ff6b6b;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 12px;
                cursor: pointer;
            `;
            debugButton.onclick = debugProfileState;
            document.body.appendChild(debugButton);
        }, 1000);
    });
}

console.log('🛠️ Profile debug utilities loaded. Available functions:');
console.log('- debugProfileState() - Check current profile state');
console.log('- forceProfileRefresh() - Force profile to reload');
console.log('- simulateProfileTimeout() - Test timeout mechanism');
console.log('- resetProfileLoadingState() - Reset stuck loading state');

// Utility Functions for Veritas Agent Application

// Toast notifications have been removed for a cleaner UI
function showToast(message, type = 'info') {
    // Log to console instead of showing UI notification
    console.log(`[${type.toUpperCase()}] ${message}`);
}

// Password visibility toggle function
function togglePasswordVisibility(inputId) {
    const passwordInput = document.getElementById(inputId);
    const eyeIcon = document.querySelector(`#${inputId} + button .fas`);
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        eyeIcon.classList.remove('fa-eye');
        eyeIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        eyeIcon.classList.remove('fa-eye-slash');
        eyeIcon.classList.add('fa-eye');
    }
}

// Form validation utilities
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validatePassword(password) {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
    return passwordRegex.test(password);
}

function validateForm(formData, rules) {
    const errors = [];
    
    for (const [field, rule] of Object.entries(rules)) {
        const value = formData[field];
        
        if (rule.required && (!value || value.trim() === '')) {
            errors.push(`${rule.label || field} is required`);
            continue;
        }
        
        if (value && rule.type === 'email' && !validateEmail(value)) {
            errors.push(`${rule.label || field} must be a valid email address`);
        }
        
        if (value && rule.type === 'password' && !validatePassword(value)) {
            errors.push(`${rule.label || field} must be at least 8 characters with uppercase, lowercase, and number`);
        }
        
        if (value && rule.minLength && value.length < rule.minLength) {
            errors.push(`${rule.label || field} must be at least ${rule.minLength} characters`);
        }
        
        if (value && rule.maxLength && value.length > rule.maxLength) {
            errors.push(`${rule.label || field} must be no more than ${rule.maxLength} characters`);
        }
    }
    
    return errors;
}

// Form error display utilities
function showFormError(formElement, message) {
    // Remove existing error message
    const existingError = formElement.querySelector('.form-error-message');
    if (existingError) {
        existingError.remove();
    }
    
    // Create error message element
    const errorDiv = document.createElement('div');
    errorDiv.className = 'form-error-message';
    errorDiv.textContent = message;
    
    // Add error styling to form inputs
    const inputs = formElement.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        input.classList.add('form-error');
        // Remove error styling after user starts typing
        input.addEventListener('input', function removeError() {
            input.classList.remove('form-error');
            input.removeEventListener('input', removeError);
        });
    });
    
    // Add error message to form
    formElement.appendChild(errorDiv);
    
    // Show error message with animation
    setTimeout(() => {
        errorDiv.classList.add('visible');
    }, 100);
    
    // Auto-hide error after 5 seconds
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.classList.remove('visible');
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.remove();
                }
            }, 300);
        }
    }, 5000);
}

function clearFormErrors(formElement) {
    const errorMessage = formElement.querySelector('.form-error-message');
    if (errorMessage) {
        errorMessage.remove();
    }
    
    const errorInputs = formElement.querySelectorAll('.form-error');
    errorInputs.forEach(input => {
        input.classList.remove('form-error');
    });
}

// Loading state utilities
function setLoadingState(element, isLoading, originalText = '') {
    if (isLoading) {
        element.disabled = true;
        element.dataset.originalText = element.textContent;
        element.innerHTML = `
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading...
        `;
    } else {
        element.disabled = false;
        element.textContent = originalText || element.dataset.originalText || 'Submit';
    }
}

// Dashboard Loading Overlay Functions
function showDashboardLoading() {
    const overlay = document.getElementById('dashboard-loading-overlay');
    if (overlay) {
        // Reset all states
        resetDashboardLoadingSteps();
        updateDashboardLoadingProgress(0, 'Initializing Dashboard', 'Setting up your workspace...');

        // Show overlay
        overlay.classList.remove('hidden', 'hide');
        overlay.classList.add('show');

        console.log('Dashboard loading overlay shown');
    }
}

function hideDashboardLoading() {
    const overlay = document.getElementById('dashboard-loading-overlay');
    if (overlay) {
        // Complete all steps first
        completeDashboardLoadingStep('dashboard');
        updateDashboardLoadingProgress(100, 'Dashboard Ready', 'Welcome to your dashboard!');

        // Hide after a brief delay to show completion
        setTimeout(() => {
            overlay.classList.remove('show');
            overlay.classList.add('hide');

            // Completely hide after transition
            setTimeout(() => {
                overlay.classList.add('hidden');
                overlay.classList.remove('hide');
                // Ensure overlay is completely removed from interaction
                overlay.style.display = 'none';
                overlay.style.pointerEvents = 'none';
            }, 500);
        }, 800);

        console.log('Dashboard loading overlay hidden');
    }
}

// Force hide dashboard loading overlay (for emergency situations)
function forceHideDashboardLoading() {
    const overlay = document.getElementById('dashboard-loading-overlay');
    if (overlay) {
        overlay.classList.remove('show');
        overlay.classList.add('hidden', 'hide');
        overlay.style.display = 'none';
        overlay.style.pointerEvents = 'none';
        overlay.style.opacity = '0';
        overlay.style.visibility = 'hidden';
        console.log('Dashboard loading overlay force hidden');
    }
}

function updateDashboardLoadingProgress(progress, title, status) {
    const progressBar = document.getElementById('dashboard-loading-progress');
    const titleElement = document.getElementById('dashboard-loading-title');
    const statusElement = document.getElementById('dashboard-loading-status');

    if (progressBar) {
        progressBar.style.width = `${Math.min(progress, 100)}%`;
    }

    if (titleElement && title) {
        titleElement.textContent = title;
    }

    if (statusElement && status) {
        statusElement.textContent = status;
    }
}

function setDashboardLoadingStep(stepName, status = 'active') {
    const stepElement = document.getElementById(`loading-step-${stepName}`);
    if (!stepElement) return;

    const icon = stepElement.querySelector('i');
    const text = stepElement.querySelector('span');

    // Reset classes
    stepElement.classList.remove('loading-step-active', 'loading-step-completed');
    icon.classList.remove('fa-spinner', 'fa-spin', 'fa-check', 'loading-step-icon-show');
    icon.classList.add('opacity-0');

    if (status === 'active') {
        stepElement.classList.add('loading-step-active');
        icon.classList.add('fa-spinner', 'fa-spin', 'text-[#00BFFF]', 'loading-step-icon-show');
        icon.classList.remove('opacity-0');
    } else if (status === 'completed') {
        stepElement.classList.add('loading-step-completed');
        icon.classList.add('fa-check', 'text-green-500', 'loading-step-icon-show');
        icon.classList.remove('opacity-0');
    }
}

function completeDashboardLoadingStep(stepName) {
    setDashboardLoadingStep(stepName, 'completed');
}

function resetDashboardLoadingSteps() {
    const steps = ['auth', 'profile', 'permissions', 'dashboard'];
    steps.forEach(step => {
        const stepElement = document.getElementById(`loading-step-${step}`);
        if (stepElement) {
            const icon = stepElement.querySelector('i');
            stepElement.classList.remove('loading-step-active', 'loading-step-completed');
            icon.classList.remove('fa-spinner', 'fa-spin', 'fa-check', 'loading-step-icon-show');
            icon.classList.add('opacity-0');
        }
    });
}

// Smooth scroll utility
function smoothScrollTo(targetY, duration = 1200) {
    const startY = window.pageYOffset;
    const distanceY = targetY - startY;
    let startTime = null;

    function easeInOutQuad(t) {
        t /= duration / 2;
        if (t < 1) return 0.5 * t * t;
        t--;
        return -0.5 * (t * (t - 2) - 1);
    }

    function animateScroll(currentTime) {
        if (startTime === null) startTime = currentTime;
        const timeElapsed = currentTime - startTime;
        const progress = Math.min(timeElapsed / duration, 1);
        const easedProgress = easeInOutQuad(progress);

        window.scrollTo(0, startY + distanceY * easedProgress);

        if (timeElapsed < duration) {
            requestAnimationFrame(animateScroll);
        }
    }
    requestAnimationFrame(animateScroll);
}

// Local storage utilities
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
        return true;
    } catch (error) {
        console.error('Error saving to localStorage:', error);
        return false;
    }
}

function loadFromLocalStorage(key, defaultValue = null) {
    try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
        console.error('Error loading from localStorage:', error);
        return defaultValue;
    }
}

function removeFromLocalStorage(key) {
    try {
        localStorage.removeItem(key);
        return true;
    } catch (error) {
        console.error('Error removing from localStorage:', error);
        return false;
    }
}

// Date formatting utilities
function formatDate(dateString, options = {}) {
    if (!dateString) return 'N/A';

    const defaultOptions = {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };

    const formatOptions = { ...defaultOptions, ...options };

    try {
        return new Date(dateString).toLocaleDateString(undefined, formatOptions);
    } catch (error) {
        console.error('Error formatting date:', error);
        return 'Invalid Date';
    }
}

function formatTime(dateString, options = {}) {
    if (!dateString) return 'N/A';

    const defaultOptions = {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    };

    const formatOptions = { ...defaultOptions, ...options };

    try {
        return new Date(dateString).toLocaleTimeString(undefined, formatOptions);
    } catch (error) {
        console.error('Error formatting time:', error);
        return 'Invalid Time';
    }
}

function formatDateTime(dateString, options = {}) {
    if (!dateString) return 'N/A';

    const defaultOptions = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };

    const formatOptions = { ...defaultOptions, ...options };

    try {
        return new Date(dateString).toLocaleString(undefined, formatOptions);
    } catch (error) {
        console.error('Error formatting datetime:', error);
        return 'Invalid DateTime';
    }
}

function formatRelativeTime(dateString) {
    if (!dateString) return 'N/A';

    try {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
        if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
        if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months ago`;
        return `${Math.floor(diffInSeconds / 31536000)} years ago`;
    } catch (error) {
        console.error('Error formatting relative time:', error);
        return 'Invalid Date';
    }
}

function formatFileSize(bytes) {
    if (!bytes || bytes === 0) return '0 B';

    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));

    return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`;
}

function formatNumber(number, options = {}) {
    if (number === null || number === undefined) return 'N/A';

    const defaultOptions = {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    };

    const formatOptions = { ...defaultOptions, ...options };

    try {
        return new Intl.NumberFormat(undefined, formatOptions).format(number);
    } catch (error) {
        console.error('Error formatting number:', error);
        return String(number);
    }
}

function formatCurrency(amount, currency = 'USD', options = {}) {
    if (amount === null || amount === undefined) return 'N/A';

    const defaultOptions = {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    };

    const formatOptions = { ...defaultOptions, ...options };

    try {
        return new Intl.NumberFormat(undefined, formatOptions).format(amount);
    } catch (error) {
        console.error('Error formatting currency:', error);
        return `${currency} ${amount}`;
    }
}

function formatPercentage(value, options = {}) {
    if (value === null || value === undefined) return 'N/A';

    const defaultOptions = {
        style: 'percent',
        minimumFractionDigits: 1,
        maximumFractionDigits: 1
    };

    const formatOptions = { ...defaultOptions, ...options };

    try {
        return new Intl.NumberFormat(undefined, formatOptions).format(value / 100);
    } catch (error) {
        console.error('Error formatting percentage:', error);
        return `${value}%`;
    }
}

// Text utility functions
function truncateText(text, maxLength = 100, suffix = '...') {
    if (!text || typeof text !== 'string') return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - suffix.length) + suffix;
}

function capitalizeFirst(text) {
    if (!text || typeof text !== 'string') return '';
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
}

function capitalizeWords(text) {
    if (!text || typeof text !== 'string') return '';
    return text.split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(' ');
}

function slugify(text) {
    if (!text || typeof text !== 'string') return '';
    return text
        .toLowerCase()
        .trim()
        .replace(/[^\w\s-]/g, '') // Remove special characters
        .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
        .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

// Debounce utility for search/input handling
function debounce(func, wait, immediate = false) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
}

// Copy to clipboard utility
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showToast('Copied to clipboard!', 'success', 2000);
        return true;
    } catch (error) {
        console.error('Failed to copy to clipboard:', error);
        showToast('Failed to copy to clipboard', 'error');
        return false;
    }
}

// Generate random ID utility
function generateId(prefix = 'id', length = 8) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = prefix + '_';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// Clear user data utility
function clearUserData() {
    console.log('Clearing user data...');

    // Reset currentUser object
    currentUser = {
        id: null,
        email: null,
        fullName: null,
        company: null,
        role: 'Guest'
    };

    // Clear any cached data (removed caching system)
    window.cachedUserProfile = null;
    window.cachedNotifications = null;
    window.overviewContentLoaded = false; // Reset overview content flag

    // Clear authentication state variables
    if (window.isLoggedIn !== undefined) {
        window.isLoggedIn = false;
    }

    // Clear profile loading state to prevent stuck loading
    if (window.isProfileLoading !== undefined) {
        window.isProfileLoading = false;
    }

    // Clear any profile loading timeout
    if (window.profileLoadingTimeout) {
        clearTimeout(window.profileLoadingTimeout);
        window.profileLoadingTimeout = null;
    }

    // Clear localStorage items (be more specific about what we clear)
    try {
        // Clear Supabase auth tokens
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && (key.startsWith('supabase.auth') || key.startsWith('sb-'))) {
                keysToRemove.push(key);
            }
        }
        keysToRemove.forEach(key => removeFromLocalStorage(key));
    } catch (error) {
        console.warn('Error clearing localStorage:', error);
    }

    // Clear dashboard-specific data
    clearDashboardData();

    console.log('User data cleared successfully');
}

// Clear all dashboard-specific cached data and UI elements
function clearDashboardData() {
    console.log('Clearing dashboard data for user session change...');

    // Clear notification-related data
    if (window.notificationOffset !== undefined) {
        window.notificationOffset = 0;
    }

    // Clear notification containers
    const notificationContainer = document.getElementById('notifications-container');
    if (notificationContainer) {
        notificationContainer.innerHTML = '';
    }

    // Clear notification statistics
    const todayCount = document.getElementById('today-activity-count');
    const unreadCount = document.getElementById('unread-count');
    const weekCount = document.getElementById('week-activity-count');
    if (todayCount) todayCount.textContent = '-';
    if (unreadCount) unreadCount.textContent = '-';
    if (weekCount) weekCount.textContent = '-';

    // Clear notification badge
    const notificationBadge = document.getElementById('notification-badge');
    if (notificationBadge) {
        notificationBadge.classList.add('hidden');
        notificationBadge.textContent = '';
    }

    // Clear overview content
    const overviewAgentsContainer = document.getElementById('overview-agents-container');
    if (overviewAgentsContainer) {
        overviewAgentsContainer.innerHTML = '';
    }

    // Clear company selection
    const companySelection = document.getElementById('overview-company-selection');
    if (companySelection) {
        companySelection.innerHTML = '';
    }

    // Clear integration statuses
    const integrationCards = document.querySelectorAll('.integration-card');
    integrationCards.forEach(card => {
        const statusBadge = card.querySelector('.status-badge');
        const connectBtn = card.querySelector('.connect-btn');
        const disconnectBtn = card.querySelector('.disconnect-btn');

        if (statusBadge) {
            statusBadge.className = 'status-badge disconnected px-2 py-1 rounded-full text-xs';
            statusBadge.textContent = 'Disconnected';
        }
        if (connectBtn) {
            connectBtn.classList.remove('hidden', 'connected');
            connectBtn.disabled = false;
        }
        if (disconnectBtn) {
            disconnectBtn.classList.add('hidden');
            disconnectBtn.disabled = false;
        }
    });

    // Clear any chart instances
    if (window.agentStatusChartInstance) {
        window.agentStatusChartInstance.destroy();
        window.agentStatusChartInstance = null;
    }

    console.log('Dashboard data cleared successfully');
}

// Make utility functions globally available
window.formatDate = formatDate;
window.formatTime = formatTime;
window.formatDateTime = formatDateTime;
window.formatRelativeTime = formatRelativeTime;
window.formatFileSize = formatFileSize;
window.formatNumber = formatNumber;
window.formatCurrency = formatCurrency;
window.formatPercentage = formatPercentage;
window.truncateText = truncateText;
window.capitalizeFirst = capitalizeFirst;
window.capitalizeWords = capitalizeWords;
window.slugify = slugify;
window.generateId = generateId;
window.clearUserData = clearUserData;
window.clearDashboardData = clearDashboardData;

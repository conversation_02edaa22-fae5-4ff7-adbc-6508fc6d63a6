<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google OAuth Callback - Veritas Agent</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: #111111;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .container {
            text-align: center;
            padding: 2rem;
        }
        .spinner {
            border: 4px solid #333;
            border-top: 4px solid #00BFFF;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error {
            color: #ef4444;
            margin-top: 1rem;
        }
        .success {
            color: #10b981;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <h2>Processing Google OAuth...</h2>
        <p id="status">Connecting your Gmail account...</p>
        <div id="message"></div>
    </div>

    <script>
        // Handle Google OAuth callback
        async function handleOAuthCallback() {
            try {
                const urlParams = new URLSearchParams(window.location.search);
                const authCode = urlParams.get('code');
                const state = urlParams.get('state');
                const error = urlParams.get('error');
                
                // Check for errors
                if (error) {
                    throw new Error(`OAuth error: ${error}`);
                }
                
                // Verify state parameter
                const storedState = window.opener?.sessionStorage.getItem('oauth_state');
                if (!state || state !== storedState) {
                    throw new Error('Invalid OAuth state parameter');
                }
                
                if (!authCode) {
                    throw new Error('No authorization code received');
                }
                
                document.getElementById('status').textContent = 'Authorization successful! Exchanging code for tokens...';
                
                // Exchange authorization code for tokens
                const tokenData = await exchangeCodeForTokens(authCode);
                
                document.getElementById('status').textContent = 'Getting user profile...';
                
                // Get user profile information
                const userProfile = await getUserProfile(tokenData.access_token);
                
                document.getElementById('status').textContent = 'Saving connection data...';
                
                // Store the connection data in session storage for parent window
                if (window.opener) {
                    window.opener.sessionStorage.setItem('oauth_token_data', JSON.stringify(tokenData));
                    window.opener.sessionStorage.setItem('oauth_user_profile', JSON.stringify(userProfile));
                }
                
                // Success
                document.getElementById('status').textContent = 'Gmail connected successfully!';
                document.getElementById('message').innerHTML = '<div class="success">You can close this window.</div>';
                
                // Notify parent window
                if (window.opener) {
                    window.opener.sessionStorage.setItem('oauth_auth_code', authCode);
                    window.opener.sessionStorage.setItem('oauth_success', 'true');
                    window.opener.sessionStorage.removeItem('oauth_state');
                }
                
                // Close popup after 2 seconds
                setTimeout(() => {
                    window.close();
                }, 2000);
                
            } catch (error) {
                console.error('OAuth callback error:', error);
                document.getElementById('status').textContent = 'Connection failed';
                document.getElementById('message').innerHTML = `<div class="error">${error.message}</div>`;
                
                // Notify parent window of error
                if (window.opener) {
                    window.opener.sessionStorage.setItem('oauth_error', error.message);
                    window.opener.sessionStorage.removeItem('oauth_state');
                }
                
                // Close popup after 3 seconds
                setTimeout(() => {
                    window.close();
                }, 3000);
            }
        }
        
        // Exchange authorization code for access tokens
        async function exchangeCodeForTokens(authCode) {
            const response = await fetch('/api/oauth/google/token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    code: authCode,
                    redirect_uri: window.location.origin + '/oauth/google/callback'
                })
            });
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.error || 'Failed to exchange authorization code');
            }
            
            return await response.json();
        }
        
        // Get user profile from Google
        async function getUserProfile(accessToken) {
            const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
                headers: {
                    'Authorization': `Bearer ${accessToken}`
                }
            });
            
            if (!response.ok) {
                throw new Error('Failed to get user profile');
            }
            
            return await response.json();
        }
        
        // Store Gmail connection data (placeholder - will be handled by parent window)
        async function storeGmailConnectionData(tokenData, userProfile) {
            // This will be handled by the parent window after popup closes
            console.log('Token data and user profile ready for storage');
        }
        
        // Start the callback handling when page loads
        window.addEventListener('load', handleOAuthCallback);
    </script>
</body>
</html>

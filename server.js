const express = require('express');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 8000;

// Google OAuth configuration
const GOOGLE_OAUTH_CONFIG = {
    clientId: '363533033274-4m8rdp8k6i4sn91cj8sdvu0e6oakhvgo.apps.googleusercontent.com',
    clientSecret: '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',
    tokenUrl: 'https://oauth2.googleapis.com/token'
};

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// OAuth token exchange endpoint
app.post('/api/oauth/google/token', async (req, res) => {
    try {
        const { code, redirect_uri } = req.body;
        
        if (!code) {
            return res.status(400).json({ error: 'Authorization code is required' });
        }
        
        console.log('Exchanging authorization code for tokens...');
        
        // Exchange authorization code for tokens
        const tokenResponse = await fetch(GOOGLE_OAUTH_CONFIG.tokenUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                client_id: GOOGLE_OAUTH_CONFIG.clientId,
                client_secret: GOOGLE_OAUTH_CONFIG.clientSecret,
                code: code,
                grant_type: 'authorization_code',
                redirect_uri: redirect_uri
            })
        });
        
        if (!tokenResponse.ok) {
            const errorData = await tokenResponse.text();
            console.error('Token exchange failed:', errorData);
            return res.status(400).json({ error: 'Failed to exchange authorization code' });
        }
        
        const tokenData = await tokenResponse.json();
        
        console.log('Token exchange successful');
        
        // Return tokens (in production, you'd want to handle these more securely)
        res.json({
            access_token: tokenData.access_token,
            refresh_token: tokenData.refresh_token,
            expires_in: tokenData.expires_in,
            scope: tokenData.scope,
            token_type: tokenData.token_type
        });
        
    } catch (error) {
        console.error('OAuth token exchange error:', error);
        res.status(500).json({ error: 'Internal server error during token exchange' });
    }
});

// Serve the main application
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Handle OAuth callback route
app.get('/oauth/google/callback', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'oauth', 'google', 'callback.html'));
});

// Start server
app.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
    console.log('Google OAuth Client ID:', GOOGLE_OAUTH_CONFIG.clientId);
    console.log('OAuth Callback URL: http://localhost:' + PORT + '/oauth/google/callback');
});

module.exports = app;
